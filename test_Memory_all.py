from camel.memories import (
    LongtermAgentMemory,#核心记忆管理器，负责存储和检索对话历史
    MemoryRecord,
    ScoreBasedContextCreator,#基于评分的上下文创建器
    ChatHistoryBlock,#存储聊天历史记录的数据块
    VectorDBBlock,#向量数据库块
)
#载入基础配置
from camel.messages import BaseMessage
from camel.types import ModelType, OpenAIBackendRole
from camel.utils import OpenAITokenCounter
from camel.embeddings import SentenceTransformerEncoder
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType

#载入环境配置
import os
from dotenv import load_dotenv
load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

#初始化记忆系统
memory = LongtermAgentMemory(
    context_creator=ScoreBasedContextCreator(
        token_counter=OpenAITokenCounter(ModelType.GPT_4O_MINI),
        token_limit=1024,  # 使用 token 计数器限制上下文长度（1024 tokens）
    ),
    chat_history_block=ChatHistoryBlock(),  # 聊天历史存储
    vector_db_block=VectorDBBlock(
        embedding=SentenceTransformerEncoder(model_name="BAAI/bge-m3")
    ),  # 向量数据库存储，使用 BGE-M3 模型进行文本嵌入
)

#创建记忆记录
records = [
    MemoryRecord(
        message=BaseMessage.make_user_message(
            role_name="User",
            content="什么是CAMEL AI?"
        ),
        role_at_backend=OpenAIBackendRole.USER,
    ),
    MemoryRecord(
        message=BaseMessage.make_assistant_message(
            role_name="Agent",
            content="CAMEL-AI是第一个LLM多智能体框架,并且是一个致力于寻找智能体 scaling law 的开源社区。"
        ),
        role_at_backend=OpenAIBackendRole.ASSISTANT,
    ),
]

#写入记忆数据
memory.write_records(records)

#验证记忆系统
context, token_count = memory.get_context()
print(f"上下文: {context}")
print(f"Token 消耗: {token_count}")


###创建基础智能体
# 定义系统消息
sys_msg = "你是一个好奇的智能体，正在探索宇宙的奥秘。"

# 初始化模型
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

# 创建智能体
agent = ChatAgent(system_message=sys_msg, model=model)

###测试无记忆状态
usr_msg = "告诉我基于我们讨论的内容，哪个是第一个LLM多智能体框架？"
response = agent.step(usr_msg)
print("无记忆状态响应:", response.msgs[0].content)

# 关键步骤：将记忆系统赋值给智能体
agent.memory = memory

# 使用相同的问题测试
response = agent.step(usr_msg)
print("集成记忆后响应:", response.msgs[0].content)
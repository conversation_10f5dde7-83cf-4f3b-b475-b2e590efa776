#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复删除emoji后的缩进问题
"""

def fix_indentation(file_path):
    """修复文件的缩进问题"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    in_class = False
    in_function = False
    class_indent = 0
    function_indent = 0
    
    for i, line in enumerate(lines):
        # 检测类定义
        if line.strip().startswith('class '):
            in_class = True
            class_indent = len(line) - len(line.lstrip())
            fixed_lines.append(line)
            continue
        
        # 检测函数定义
        if line.strip().startswith('def '):
            in_function = True
            if in_class:
                function_indent = class_indent + 4
            else:
                function_indent = len(line) - len(line.lstrip())
            fixed_lines.append(line)
            continue
        
        # 处理docstring
        if line.strip() == '""""""':
            if in_function:
                fixed_lines.append(' ' * (function_indent + 4) + '"""智能重试装饰器"""\n')
            elif in_class:
                fixed_lines.append(' ' * (class_indent + 4) + '"""类说明"""\n')
            else:
                fixed_lines.append(line)
            continue
        
        # 处理其他行
        if line.strip():
            # 如果在函数内部
            if in_function and not line.startswith(' ' * function_indent):
                if line.strip() in ['pass', 'return', 'break', 'continue'] or line.strip().startswith('self.') or line.strip().startswith('return '):
                    fixed_lines.append(' ' * (function_indent + 4) + line.strip() + '\n')
                else:
                    fixed_lines.append(line)
            # 如果在类内部但不在函数内部
            elif in_class and not in_function and not line.startswith(' ' * class_indent):
                if line.strip() in ['pass'] or line.strip().startswith('def '):
                    fixed_lines.append(' ' * (class_indent + 4) + line.strip() + '\n')
                else:
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
            # 空行可能表示函数或类的结束
            if in_function:
                in_function = False
            if in_class and not in_function:
                # 检查下一行是否还在类内部
                if i + 1 < len(lines) and lines[i + 1].strip() and not lines[i + 1].startswith(' '):
                    in_class = False
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"已修复 {file_path} 的缩进问题")

if __name__ == "__main__":
    fix_indentation("CAMEL_Prompt_task2_workforce.py")
    print("缩进修复完成！")
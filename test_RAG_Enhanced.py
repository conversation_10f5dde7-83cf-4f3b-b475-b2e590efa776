"""
增强版RAG系统：集成查询重写(Rewriting)和假设文档嵌入(HyDE)

该脚本实现了一个增强的RAG系统，结合了以下技术：
1. 查询重写(Query Rewriting)：修正用户查询中的错别字和表达问题
2. HyDE (Hypothetical Document Embeddings)：生成假设文档来改善检索效果
3. 传统RAG检索和生成

HyDE方法的核心思想：
- 使用LLM为用户查询生成假设文档
- 这些假设文档虽然可能包含错误，但与知识库中的真实文档相关联
- 通过假设文档的向量表示来检索相似的真实文档，提高检索准确性
"""

import os
import requests
import numpy as np
import json
from typing import List, Dict, Any

# 导入CAMEL框架相关模块
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from camel.agents import ChatAgent
from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever, BM25Retriever
from camel.storages.vectordb_storages import QdrantStorage

# 导入环境变量模块
from dotenv import load_dotenv

# 导入评估相关模块
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# 可选的评估依赖
try:
    from rouge_score import rouge_scorer
    HAS_ROUGE = True
except ImportError:
    print("⚠️ rouge-score 未安装，跳过ROUGE评分")
    HAS_ROUGE = False

try:
    import nltk
    from nltk.translate.bleu_score import sentence_bleu
    from nltk.tokenize import word_tokenize
    HAS_NLTK = True
except ImportError:
    print("⚠️ NLTK 未安装，跳过BLEU评分")
    HAS_NLTK = False

class RAGEvaluator:
    """
    RAG系统评估器

    支持检索模块和生成模块的全面评估，包括：
    1. 检索评估：精确率、召回率、F1值、相似度
    2. 生成评估：BLEU、ROUGE、语义相似度
    3. 多种相似度计算方法：TF-IDF、Embedding
    """

    def __init__(self, embedding_model, similarity_threshold: float = 0.3):
        """
        初始化评估器

        Args:
            embedding_model: 嵌入模型，用于语义相似度计算
            similarity_threshold: 相似度阈值
        """
        self.embedding_model = embedding_model
        self.similarity_threshold = similarity_threshold

        # 初始化ROUGE评分器
        if HAS_ROUGE:
            self.rouge_scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
        else:
            self.rouge_scorer = None

        print(f"✓ 评估器初始化完成，阈值: {similarity_threshold}")

    def compute_similarity_tfidf(self, expected: str, retrieved: str) -> float:
        """使用TF-IDF计算相似度"""
        if not expected or not retrieved:
            return 0.0

        try:
            vectorizer = TfidfVectorizer()
            tfidf = vectorizer.fit_transform([expected, retrieved])
            similarity_matrix = cosine_similarity(tfidf, tfidf)
            return similarity_matrix[0, 1]
        except Exception as e:
            print(f"TF-IDF相似度计算出错: {e}")
            return 0.0

    def compute_similarity_embedding(self, expected: str, retrieved: str) -> float:
        """使用Embedding模型计算语义相似度"""
        if not expected or not retrieved:
            return 0.0

        try:
            embeddings = self.embedding_model.embed_list([expected, retrieved])
            return cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
        except Exception as e:
            print(f"Embedding相似度计算出错: {e}")
            return 0.0

    def compute_similarity(self, expected: str, retrieved: str, method: str = 'embedding') -> float:
        """
        计算相似度

        Args:
            expected: 预期文本
            retrieved: 检索到的文本
            method: 计算方法 ('tfidf' 或 'embedding')

        Returns:
            相似度分数 (0-1之间)
        """
        if method == 'tfidf':
            return self.compute_similarity_tfidf(expected, retrieved)
        elif method == 'embedding':
            return self.compute_similarity_embedding(expected, retrieved)
        else:
            # 默认使用embedding方法
            return self.compute_similarity_embedding(expected, retrieved)

    def calculate_precision(self, retrieved: List[str], relevant: List[str],
                          threshold: float = None, method: str = 'embedding') -> float:
        """
        计算精确率（Precision）

        Args:
            retrieved: 检索到的文档列表
            relevant: 相关文档列表
            threshold: 相似度阈值
            method: 相似度计算方法

        Returns:
            精确率分数
        """
        if not retrieved:
            return 0.0

        threshold = threshold or self.similarity_threshold
        correct = 0

        for r in retrieved:
            for rel in relevant:
                similarity = self.compute_similarity(rel, r, method)
                if similarity >= threshold:
                    correct += 1
                    break

        return correct / len(retrieved)

    def calculate_recall(self, retrieved: List[str], relevant: List[str],
                        threshold: float = None, method: str = 'embedding') -> float:
        """
        计算召回率（Recall）

        Args:
            retrieved: 检索到的文档列表
            relevant: 相关文档列表
            threshold: 相似度阈值
            method: 相似度计算方法

        Returns:
            召回率分数
        """
        if not relevant:
            return 0.0

        threshold = threshold or self.similarity_threshold
        correct = 0

        for rel in relevant:
            for r in retrieved:
                similarity = self.compute_similarity(rel, r, method)
                if similarity >= threshold:
                    correct += 1
                    break

        return correct / len(relevant)

    def calculate_f1(self, precision: float, recall: float) -> float:
        """计算F1值"""
        if precision + recall == 0:
            return 0.0
        return 2 * (precision * recall) / (precision + recall)

    def evaluate_retrieval(self, query: str, expected_answers: List[str],
                          retrieved_texts: List[str], threshold: float = None,
                          method: str = 'embedding') -> Dict[str, Any]:
        """
        评估单个查询的检索质量

        Args:
            query: 查询文本
            expected_answers: 预期答案列表
            retrieved_texts: 检索到的文本列表
            threshold: 相似度阈值
            method: 相似度计算方法

        Returns:
            评估结果字典
        """
        threshold = threshold or self.similarity_threshold

        # 计算精确率、召回率和F1值
        precision = self.calculate_precision(retrieved_texts, expected_answers, threshold, method)
        recall = self.calculate_recall(retrieved_texts, expected_answers, threshold, method)
        f1 = self.calculate_f1(precision, recall)

        # 计算平均相似度
        similarities = []
        for expected in expected_answers:
            for retrieved in retrieved_texts:
                similarities.append(self.compute_similarity(expected, retrieved, method))

        avg_similarity = np.mean(similarities) if similarities else 0.0

        return {
            "query": query,
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "avg_similarity": avg_similarity,
            "retrieved_texts": retrieved_texts,
            "expected_answers": expected_answers,
            "threshold": threshold,
            "method": method
        }

    def evaluate_generation(self, query: str, generated_answer: str,
                           reference_answers: List[str]) -> Dict[str, Any]:
        """
        评估生成模块的回答质量

        Args:
            query: 原始查询
            generated_answer: 生成的答案
            reference_answers: 参考答案列表

        Returns:
            生成质量评估结果
        """
        if not generated_answer or not reference_answers:
            return {"error": "生成答案或参考答案为空"}

        results = {
            "query": query,
            "generated_answer": generated_answer,
            "reference_answers": reference_answers,
            "semantic_similarity": 0.0,
            "bleu_score": 0.0,
            "rouge_scores": {},
            "length_ratio": 0.0,
            "quality_score": 0.0
        }

        try:
            # 1. 语义相似度评估
            max_semantic_sim = 0.0
            for ref in reference_answers:
                sim = self.compute_similarity(ref, generated_answer, 'embedding')
                max_semantic_sim = max(max_semantic_sim, sim)
            results["semantic_similarity"] = max_semantic_sim

            # 2. BLEU分数评估
            if HAS_NLTK and len(reference_answers) > 0:
                try:
                    # 将参考答案作为参考，计算BLEU分数
                    references = [word_tokenize(answer.lower()) for answer in reference_answers]
                    candidate = word_tokenize(generated_answer.lower())
                    bleu = sentence_bleu(references, candidate, weights=(0.5, 0.3, 0.2))
                    results["bleu_score"] = bleu
                except Exception as e:
                    print(f"BLEU计算失败: {e}")
                    results["bleu_score"] = 0.0

            # 3. ROUGE分数评估
            if self.rouge_scorer:
                rouge_scores = {}
                for ref in reference_answers:
                    scores = self.rouge_scorer.score(ref, generated_answer)
                    for metric, score in scores.items():
                        if metric not in rouge_scores:
                            rouge_scores[metric] = []
                        rouge_scores[metric].append(score.fmeasure)

                # 取平均值
                for metric in rouge_scores:
                    rouge_scores[metric] = np.mean(rouge_scores[metric])

                results["rouge_scores"] = rouge_scores

            # 4. 长度比率
            avg_ref_length = np.mean([len(ans.split()) for ans in reference_answers])
            generated_length = len(generated_answer.split())
            results["length_ratio"] = generated_length / (avg_ref_length + 1e-8)

            # 5. 综合质量分数
            quality_score = (
                0.4 * max_semantic_sim +
                0.3 * results["bleu_score"] +
                0.2 * results["rouge_scores"].get("rougeL", 0.0) +
                0.1 * min(1.0, results["length_ratio"])  # 长度适中加分
            )
            results["quality_score"] = quality_score

        except Exception as e:
            print(f"评估生成答案时出错: {e}")
            results["error"] = str(e)

        return results

    def evaluate_multiple_queries(self, test_cases: List[Dict[str, Any]],
                                 retrieval_function, generation_function = None,
                                 threshold: float = None, method: str = 'embedding') -> Dict[str, Any]:
        """
        评估多个查询的检索和生成质量

        Args:
            test_cases: 测试用例列表，每个包含'query'和'expected_answers'
            retrieval_function: 检索函数，接受query参数返回检索结果
            generation_function: 生成函数，接受query和retrieved_docs参数返回生成答案
            threshold: 相似度阈值
            method: 相似度计算方法

        Returns:
            整体评估结果
        """
        threshold = threshold or self.similarity_threshold
        retrieval_results = []
        generation_results = []

        print("开始评估多个查询...")
        print("=" * 60)

        for i, test_case in enumerate(test_cases):
            query = test_case["query"]
            expected_answers = test_case["expected_answers"]

            print(f"\n评估查询 {i+1}: {query}")
            print("-" * 40)

            try:
                # 执行检索
                retrieved_results = retrieval_function(query)
                if isinstance(retrieved_results, list) and len(retrieved_results) > 0:
                    if isinstance(retrieved_results[0], dict):
                        retrieved_texts = [result.get("text", str(result)) for result in retrieved_results]
                    else:
                        retrieved_texts = [str(result) for result in retrieved_results]
                else:
                    retrieved_texts = []

                # 评估检索质量
                retrieval_eval = self.evaluate_retrieval(
                    query, expected_answers, retrieved_texts, threshold, method
                )
                retrieval_results.append(retrieval_eval)

                print(f"检索评估:")
                print(f"  精确率: {retrieval_eval['precision']:.4f}")
                print(f"  召回率: {retrieval_eval['recall']:.4f}")
                print(f"  F1分数: {retrieval_eval['f1']:.4f}")
                print(f"  平均相似度: {retrieval_eval['avg_similarity']:.4f}")

                # 如果提供了生成函数，评估生成质量
                if generation_function:
                    generated_answer = generation_function(query, retrieved_results)
                    generation_eval = self.evaluate_generation(query, generated_answer, expected_answers)
                    generation_results.append(generation_eval)

                    print(f"生成评估:")
                    print(f"  语义相似度: {generation_eval.get('semantic_similarity', 0):.4f}")
                    print(f"  BLEU分数: {generation_eval.get('bleu_score', 0):.4f}")
                    print(f"  质量分数: {generation_eval.get('quality_score', 0):.4f}")

            except Exception as e:
                print(f"评估出错: {e}")
                continue

        # 计算整体评估结果
        overall_results = {
            "retrieval_results": retrieval_results,
            "generation_results": generation_results,
            "threshold": threshold,
            "method": method,
            "num_queries": len(test_cases)
        }

        if retrieval_results:
            overall_results["retrieval_metrics"] = {
                "average_precision": np.mean([r["precision"] for r in retrieval_results]),
                "average_recall": np.mean([r["recall"] for r in retrieval_results]),
                "average_f1": np.mean([r["f1"] for r in retrieval_results]),
                "average_similarity": np.mean([r["avg_similarity"] for r in retrieval_results])
            }

        if generation_results:
            overall_results["generation_metrics"] = {
                "average_semantic_similarity": np.mean([r.get("semantic_similarity", 0) for r in generation_results]),
                "average_bleu": np.mean([r.get("bleu_score", 0) for r in generation_results]),
                "average_quality": np.mean([r.get("quality_score", 0) for r in generation_results])
            }

        # 打印整体结果
        print("\n" + "=" * 60)
        print("整体评估结果:")
        if "retrieval_metrics" in overall_results:
            metrics = overall_results["retrieval_metrics"]
            print(f"检索模块:")
            print(f"  平均精确率: {metrics['average_precision']:.4f}")
            print(f"  平均召回率: {metrics['average_recall']:.4f}")
            print(f"  平均F1分数: {metrics['average_f1']:.4f}")
            print(f"  平均相似度: {metrics['average_similarity']:.4f}")

        if "generation_metrics" in overall_results:
            metrics = overall_results["generation_metrics"]
            print(f"生成模块:")
            print(f"  平均语义相似度: {metrics['average_semantic_similarity']:.4f}")
            print(f"  平均BLEU分数: {metrics['average_bleu']:.4f}")
            print(f"  平均质量分数: {metrics['average_quality']:.4f}")

        print("=" * 60)

        return overall_results

class EnhancedRAGSystem:
    """
    增强版RAG系统类

    集成了查询重写、HyDE、ReRank(RRF算法)和传统RAG功能
    """

    def __init__(self, api_key: str):
        """
        初始化增强版RAG系统

        Args:
            api_key: ModelScope API密钥
        """
        self.api_key = api_key

        # 初始化嵌入模型
        self.embedding_model = SentenceTransformerEncoder(
            model_name='intfloat/e5-large-v2'
        )

        # 初始化向量存储
        self.vector_storage = QdrantStorage(
            vector_dim=self.embedding_model.get_output_dim(),
            collection="enhanced_rag_collection",
            path="storage_enhanced_rag",
            collection_name="增强RAG知识库"
        )

        # 初始化向量检索器
        self.vector_retriever = VectorRetriever(
            embedding_model=self.embedding_model,
            storage=self.vector_storage
        )

        # 初始化BM25检索器（用于ReRank）
        self.bm25_retriever = BM25Retriever()

        # 初始化LLM模型
        self.model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
            model_type="Qwen/Qwen2.5-72B-Instruct",
            url='https://api-inference.modelscope.cn/v1/',
            api_key=self.api_key
        )

        # 初始化各种代理
        self._init_agents()

        # 初始化评估器
        self.evaluator = RAGEvaluator(
            embedding_model=self.embedding_model,
            similarity_threshold=0.3
        )

    def rrf(self, vector_results: List[Dict], text_results: List[Dict], k: int = 10, m: int = 60) -> List[tuple]:
        """
        使用RRF (Reciprocal Rank Fusion) 算法对两组检索结果进行重排序

        RRF算法通过结合不同检索方法的排名来提高检索效果。
        公式：RRF_score = Σ(1/(rank + m))，其中m是超参数

        Args:
            vector_results: 向量召回的结果列表，每个元素是包含'text'的字典
            text_results: 文本召回的结果列表，每个元素是包含'text'的字典
            k: 排序后返回前k个结果
            m: RRF算法的超参数，用于平滑排名分数

        Returns:
            重排序后的结果列表，每个元素是(文档内容, 融合分数)的元组
        """
        doc_scores = {}

        # 处理向量检索结果
        # 为每个文档分配基于排名的分数：1/(rank + m)
        for rank, result in enumerate(vector_results):
            text = result['text']
            doc_scores[text] = doc_scores.get(text, 0) + 1 / (rank + m)

        # 处理文本检索结果
        # 累加来自不同检索方法的分数
        for rank, result in enumerate(text_results):
            text = result['text']
            doc_scores[text] = doc_scores.get(text, 0) + 1 / (rank + m)

        # 按融合分数降序排序并返回前k个结果
        sorted_results = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:k]

        print(f"RRF融合完成，共处理 {len(doc_scores)} 个唯一文档，返回前 {len(sorted_results)} 个结果")

        return sorted_results

    def _init_agents(self):
        """初始化不同功能的AI代理"""
        
        # 查询重写代理
        rewriting_sys_msg = """
        你是RAG模块中的Rewriting助手，目的是理解用户的提问，并且重新组织和优化用户的提问表达，
        修正用户输入中可能存在的错别字的情况并重构提问来使得句子表达更加通顺严谨。
        请直接输出重写后的查询，不要添加额外的解释。
        """
        self.rewriting_agent = ChatAgent(
            system_message=rewriting_sys_msg,
            model=self.model
        )
        
        # HyDE假设文档生成代理
        hyde_sys_msg = """
        你是一个专门生成假设文档的助手。根据用户的查询，生成一个相关的假设文档片段。
        这个文档应该：
        1. 直接回答用户的问题
        2. 包含相关的技术细节和概念
        3. 使用专业但清晰的语言
        4. 长度适中（200-400字）
        
        请直接输出假设文档内容，不要添加额外的解释或格式。
        """
        self.hyde_agent = ChatAgent(
            system_message=hyde_sys_msg,
            model=self.model
        )
        
        # RAG回答生成代理
        rag_sys_msg = """
        你是一个帮助回答问题的助手。
        我会给你原始查询和检索到的上下文信息。
        请根据检索到的上下文回答原始查询。
        如果上下文信息不足以回答问题，请说"根据提供的信息，我无法完全回答这个问题"。
        请确保回答准确、完整且有条理。
        """
        self.rag_agent = ChatAgent(
            system_message=rag_sys_msg,
            model=self.model
        )
    
    def setup_knowledge_base(self, pdf_url: str = None, pdf_path: str = None):
        """
        设置知识库，同时初始化向量检索器和BM25检索器

        Args:
            pdf_url: PDF文件的URL
            pdf_path: 本地PDF文件路径
        """
        # 创建本地数据目录
        os.makedirs('local_data', exist_ok=True)

        if pdf_url:
            # 下载PDF文件
            print("正在下载PDF文件...")
            response = requests.get(pdf_url)
            pdf_path = 'local_data/knowledge_base.pdf'
            with open(pdf_path, 'wb') as file:
                file.write(response.content)
            print(f"PDF文件已下载到: {pdf_path}")

        if pdf_path:
            # 处理PDF文件并建立向量数据库
            print("正在处理PDF文件并建立向量数据库...")
            self.vector_retriever.process(content=pdf_path)

            # 同时为BM25检索器处理文档
            print("正在为BM25检索器处理文档...")
            self.bm25_retriever.process(content_input_path=pdf_path)

            print("知识库设置完成！（向量检索器 + BM25检索器）")
    
    def rewrite_query(self, original_query: str) -> str:
        """
        重写用户查询
        
        Args:
            original_query: 原始用户查询
            
        Returns:
            重写后的查询
        """
        rewrite_prompt = f"用户的原始提问如下：{original_query}"
        response = self.rewriting_agent.step(rewrite_prompt)
        rewritten_query = response.msgs[0].content.strip()
        
        print(f"原始查询: {original_query}")
        print(f"重写查询: {rewritten_query}")
        
        return rewritten_query
    
    def generate_hypothetical_document(self, query: str) -> str:
        """
        生成假设文档 (HyDE方法)
        
        Args:
            query: 用户查询
            
        Returns:
            生成的假设文档
        """
        hyde_prompt = f"请为以下查询生成一个相关的假设文档：{query}"
        response = self.hyde_agent.step(hyde_prompt)
        hypothetical_doc = response.msgs[0].content.strip()
        
        print(f"生成的假设文档:\n{hypothetical_doc}")
        
        return hypothetical_doc

    def retrieve_with_rrf(self, query: str, top_k: int = 10, rrf_k: int = 5, m: int = 60) -> List[Dict[str, Any]]:
        """
        使用RRF算法结合向量检索和BM25检索的结果

        Args:
            query: 用户查询
            top_k: 每个检索器返回的文档数量
            rrf_k: RRF融合后返回的文档数量
            m: RRF算法的超参数

        Returns:
            RRF融合后的检索结果列表
        """
        print(f"使用RRF算法进行混合检索...")

        # 向量检索
        print("执行向量检索...")
        vector_results = self.vector_retriever.query(query=query, top_k=top_k)

        # BM25检索
        print("执行BM25检索...")
        bm25_results = self.bm25_retriever.query(query=query, top_k=top_k)

        # 使用RRF算法融合结果
        print("使用RRF算法融合检索结果...")
        rrf_results = self.rrf(vector_results, bm25_results, k=rrf_k, m=m)

        # 转换RRF结果格式以保持与其他检索方法的一致性
        formatted_results = []
        for text, score in rrf_results:
            formatted_results.append({
                'text': text,
                'rrf_score': score,
                'source': 'RRF_fusion'
            })

        print(f"RRF检索完成，返回 {len(formatted_results)} 个文档")
        for i, result in enumerate(formatted_results):
            print(f"文档 {i+1} (RRF分数: {result['rrf_score']:.4f}):")
            print(f"{result['text'][:200]}...\n")

        return formatted_results

    def retrieve_with_hyde_and_rrf(self, query: str, top_k: int = 10, rrf_k: int = 5, m: int = 60) -> List[Dict[str, Any]]:
        """
        结合HyDE和RRF的高级检索方法

        Args:
            query: 用户查询
            top_k: 每个检索器返回的文档数量
            rrf_k: RRF融合后返回的文档数量
            m: RRF算法的超参数

        Returns:
            HyDE+RRF融合后的检索结果列表
        """
        print(f"使用HyDE+RRF组合方法进行检索...")

        # 生成假设文档
        hypothetical_doc = self.generate_hypothetical_document(query)

        # 使用假设文档进行向量检索
        print("使用假设文档进行向量检索...")
        hyde_vector_results = self.vector_retriever.query(query=hypothetical_doc, top_k=top_k)

        # 使用原始查询进行BM25检索
        print("使用原始查询进行BM25检索...")
        bm25_results = self.bm25_retriever.query(query=query, top_k=top_k)

        # 使用RRF算法融合HyDE向量检索和BM25检索的结果
        print("使用RRF算法融合HyDE和BM25检索结果...")
        rrf_results = self.rrf(hyde_vector_results, bm25_results, k=rrf_k, m=m)

        # 转换结果格式
        formatted_results = []
        for text, score in rrf_results:
            formatted_results.append({
                'text': text,
                'rrf_score': score,
                'source': 'HyDE_RRF_fusion'
            })

        print(f"HyDE+RRF检索完成，返回 {len(formatted_results)} 个文档")
        for i, result in enumerate(formatted_results):
            print(f"文档 {i+1} (RRF分数: {result['rrf_score']:.4f}):")
            print(f"{result['text'][:200]}...\n")

        return formatted_results

    def retrieve_with_hyde(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        使用HyDE方法进行检索
        
        Args:
            query: 用户查询
            top_k: 返回的文档数量
            
        Returns:
            检索到的文档列表
        """
        # 生成假设文档
        hypothetical_doc = self.generate_hypothetical_document(query)
        
        # 使用假设文档进行检索
        hyde_results = self.vector_retriever.query(
            query=hypothetical_doc,
            top_k=top_k
        )
        
        # 同时使用原始查询进行检索作为对比
        original_results = self.vector_retriever.query(
            query=query,
            top_k=top_k
        )
        
        print(f"\n=== HyDE检索结果 ===")
        for i, result in enumerate(hyde_results):
            print(f"文档 {i+1} (相似度: {result.get('similarity', 'N/A')}):")
            print(f"{result['text'][:200]}...\n")
        
        print(f"=== 原始查询检索结果 ===")
        for i, result in enumerate(original_results):
            print(f"文档 {i+1} (相似度: {result.get('similarity', 'N/A')}):")
            print(f"{result['text'][:200]}...\n")
        
        return hyde_results, original_results
    
    def generate_answer(self, query: str, retrieved_docs: List[Dict[str, Any]]) -> str:
        """
        基于检索到的文档生成答案
        
        Args:
            query: 用户查询
            retrieved_docs: 检索到的文档列表
            
        Returns:
            生成的答案
        """
        # 构建上下文
        context = "\n\n".join([doc['text'] for doc in retrieved_docs])
        
        # 构建提示
        prompt = f"""
        原始查询: {query}
        
        检索到的上下文信息:
        {context}
        
        请根据上述上下文信息回答原始查询。
        """
        
        response = self.rag_agent.step(prompt)
        answer = response.msgs[0].content.strip()
        
        return answer
    
    def enhanced_query(self, original_query: str, use_rewriting: bool = True,
                      use_hyde: bool = True, use_rrf: bool = True, top_k: int = 10, rrf_k: int = 5) -> Dict[str, Any]:
        """
        执行增强版RAG查询

        Args:
            original_query: 原始用户查询
            use_rewriting: 是否使用查询重写
            use_hyde: 是否使用HyDE方法
            use_rrf: 是否使用RRF重排序算法
            top_k: 每个检索器返回的文档数量
            rrf_k: RRF融合后返回的文档数量

        Returns:
            包含各步骤结果的字典
        """
        print("=" * 60)
        print("开始执行增强版RAG查询")
        print("=" * 60)
        
        results = {
            'original_query': original_query,
            'rewritten_query': None,
            'hypothetical_document': None,
            'retrieved_docs': None,
            'final_answer': None
        }
        
        # 步骤1: 查询重写（可选）
        if use_rewriting:
            print("\n步骤1: 查询重写")
            print("-" * 30)
            rewritten_query = self.rewrite_query(original_query)
            results['rewritten_query'] = rewritten_query
            query_to_use = rewritten_query
        else:
            query_to_use = original_query
        
        # 步骤2: 文档检索
        print(f"\n步骤2: 文档检索")
        print("-" * 30)

        if use_rrf and use_hyde:
            print("使用HyDE+RRF组合方法进行检索...")
            retrieved_docs = self.retrieve_with_hyde_and_rrf(query_to_use, top_k=top_k, rrf_k=rrf_k)
            results['retrieved_docs'] = retrieved_docs
            results['retrieval_method'] = 'HyDE+RRF'
        elif use_rrf:
            print("使用RRF方法进行检索...")
            retrieved_docs = self.retrieve_with_rrf(query_to_use, top_k=top_k, rrf_k=rrf_k)
            results['retrieved_docs'] = retrieved_docs
            results['retrieval_method'] = 'RRF'
        elif use_hyde:
            print("使用HyDE方法进行检索...")
            hyde_results, original_results = self.retrieve_with_hyde(query_to_use, top_k)
            results['retrieved_docs'] = hyde_results
            results['original_retrieval_docs'] = original_results
            results['retrieval_method'] = 'HyDE'
            retrieved_docs = hyde_results
        else:
            print("使用传统向量检索方法...")
            retrieved_docs = self.vector_retriever.query(query=query_to_use, top_k=top_k)
            results['retrieved_docs'] = retrieved_docs
            results['retrieval_method'] = 'Traditional'
        
        # 步骤3: 答案生成
        print(f"\n步骤3: 答案生成")
        print("-" * 30)
        final_answer = self.generate_answer(query_to_use, retrieved_docs)
        results['final_answer'] = final_answer
        
        print(f"\n最终答案:\n{final_answer}")
        
        return results

    def create_evaluation_document(self) -> str:
        """
        创建用于评估的示例文档

        Returns:
            文档文件路径
        """
        content = """CAMEL AI 介绍

CAMEL AI 是一个开源的、社区驱动的AI框架，旨在简化AI应用的开发和部署。该框架提供了多种功能模块，包括数据加载、特征工程、模型训练和部署等。

主要特点

模块化设计：用户可以根据需求选择性地加载不同的功能模块。

易用性：提供了简单易用的API接口，降低了开发门槛。

扩展性：支持多种模型和后端服务，方便用户根据需求进行扩展。

常见问题

如何开始使用CAMEL AI？

首先安装框架：pip install camel-ai

引入必要的模块：from camel import *

参考官方文档：CAMEL AI官方文档

CAMEL AI支持哪些功能？

CAMEL AI支持以下核心功能：
- 多智能体协作系统
- 角色扮演对话
- 任务分解和执行
- 代码生成和调试
- 文档处理和检索

角色扮演功能如何实现？

角色扮演功能通过以下方式实现：
- 定义角色特征和行为模式
- 设置对话上下文和约束条件
- 实现角色间的交互逻辑
- 维护角色状态和记忆信息
"""

        # 确保目录存在
        os.makedirs('local_data', exist_ok=True)

        # 写入评估文档
        eval_path = 'local_data/evaluation_document.md'
        with open(eval_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"评估文档已创建: {eval_path}")
        return eval_path

    def get_evaluation_test_cases(self) -> List[Dict[str, Any]]:
        """
        获取评估测试用例

        Returns:
            测试用例列表，每个包含query和expected_answers
        """
        return [
            {
                "query": "什么是CAMEL AI？",
                "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"]
            },
            {
                "query": "如何开始使用CAMEL AI？",
                "expected_answers": ["首先安装框架：pip install camel-ai，然后引入必要的模块。"]
            },
            {
                "query": "CAMEL AI 的主要特点是什么？",
                "expected_answers": ["模块化设计、易用性和扩展性。"]
            },
            {
                "query": "CAMEL AI支持哪些功能？",
                "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"]
            },
            {
                "query": "角色扮演功能如何实现？",
                "expected_answers": ["通过定义角色特征、设置对话上下文、实现交互逻辑、维护状态和记忆来实现。"]
            }
        ]

    def setup_evaluation_knowledge_base(self, chunk_size: int = 100):
        """
        设置用于评估的知识库

        Args:
            chunk_size: 文档分块大小
        """
        # 创建评估文档
        eval_doc_path = self.create_evaluation_document()

        # 处理文档并建立索引
        print("处理评估文档并建立索引...")
        self.vector_retriever.process(
            content=eval_doc_path,
            max_characters=chunk_size,
            should_chunk=True
        )
        self.bm25_retriever.process(content_input_path=eval_doc_path)

        print("✓ 评估知识库设置完成")

    def evaluate_retrieval_methods(self, test_cases: List[Dict[str, Any]] = None,
                                  similarity_methods: List[str] = None) -> Dict[str, Any]:
        """
        评估不同检索方法的性能

        Args:
            test_cases: 测试用例列表
            similarity_methods: 相似度计算方法列表

        Returns:
            评估结果
        """
        if test_cases is None:
            test_cases = self.get_evaluation_test_cases()

        if similarity_methods is None:
            similarity_methods = ['embedding', 'tfidf']

        # 定义不同的检索方法
        retrieval_methods = {
            "传统向量检索": lambda query: self.vector_retriever.query(query=query, top_k=3),
            "HyDE检索": lambda query: self.retrieve_with_hyde(query, top_k=3)[0],  # 只取HyDE结果
            "RRF检索": lambda query: self.retrieve_with_rrf(query, top_k=10, rrf_k=3),
            "完整增强检索": lambda query: self.enhanced_query(query, use_rewriting=True, use_hyde=True, use_rrf=True)["retrieved_docs"]
        }

        evaluation_results = {}

        print("=" * 80)
        print("开始评估不同检索方法的性能")
        print("=" * 80)

        for method_name, retrieval_func in retrieval_methods.items():
            print(f"\n{'='*20} 评估 {method_name} {'='*20}")

            method_results = {}

            for sim_method in similarity_methods:
                print(f"\n使用 {sim_method.upper()} 相似度计算:")

                # 评估当前检索方法
                results = self.evaluator.evaluate_multiple_queries(
                    test_cases, retrieval_func, threshold=0.3, method=sim_method
                )

                method_results[sim_method] = results

            evaluation_results[method_name] = method_results

        return evaluation_results

    def evaluate_end_to_end(self, test_cases: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        端到端评估（检索+生成）

        Args:
            test_cases: 测试用例列表

        Returns:
            端到端评估结果
        """
        if test_cases is None:
            test_cases = self.get_evaluation_test_cases()

        print("=" * 80)
        print("开始端到端评估（检索+生成）")
        print("=" * 80)

        # 定义检索函数
        def retrieval_func(query):
            return self.enhanced_query(query, use_rewriting=True, use_hyde=True, use_rrf=True)["retrieved_docs"]

        # 定义生成函数
        def generation_func(query, retrieved_docs):
            return self.generate_answer(query, retrieved_docs)

        # 执行端到端评估
        results = self.evaluator.evaluate_multiple_queries(
            test_cases, retrieval_func, generation_func, threshold=0.3, method='embedding'
        )

        return results


def main():
    """主函数：演示增强版RAG系统的使用"""
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return
    
    # 初始化增强版RAG系统
    print("初始化增强版RAG系统...")
    rag_system = EnhancedRAGSystem(api_key)

    # 选择运行模式
    print("\n选择运行模式:")
    print("1. 演示模式 - 展示不同检索方法的效果")
    print("2. 评估模式 - 评估检索方法的性能指标")
    print("3. 端到端评估 - 评估检索+生成的完整流程")
    print("4. 完整模式 - 演示 + 评估")

    mode = input("请选择模式 (1-4，默认为1): ").strip()

    if mode in ["2", "3", "4"]:
        # 评估模式需要使用评估文档
        print("设置评估知识库...")
        rag_system.setup_evaluation_knowledge_base(chunk_size=100)
    else:
        # 演示模式使用CAMEL论文
        print("设置知识库（使用CAMEL论文）...")
        pdf_url = "https://arxiv.org/pdf/2303.17760.pdf"
        rag_system.setup_knowledge_base(pdf_url=pdf_url)
    
    if mode in ["1", "4"]:
        # 演示模式
        print("\n" + "=" * 80)
        print("演示模式：展示不同检索方法的效果")
        print("=" * 80)

        # 测试查询（包含错别字）
        test_queries = [
            "我盖如何解决CAMEL中文档冲服的问题问题呢，几个版本的文档可能存在代码结构的冲突",
            "CAMEL是什么东东？它有什么特点吗",
            "角色扮演在CAMEL中是怎么实现的"
        ]

        # 测试不同的检索方法组合
        test_methods = [
            {"name": "传统RAG", "rewriting": False, "hyde": False, "rrf": False},
            {"name": "查询重写+传统RAG", "rewriting": True, "hyde": False, "rrf": False},
            {"name": "HyDE增强RAG", "rewriting": False, "hyde": True, "rrf": False},
            {"name": "RRF重排序RAG", "rewriting": False, "hyde": False, "rrf": True},
            {"name": "完整增强RAG (Rewriting+HyDE+RRF)", "rewriting": True, "hyde": True, "rrf": True}
        ]

        for i, query in enumerate(test_queries):
            print("\n" + "=" * 100)
            print(f"测试查询 {i+1}: {query}")
            print("=" * 100)

            # 只对第一个查询测试所有方法，其他查询使用完整增强方法
            methods_to_test = test_methods if i == 0 else [test_methods[-1]]

            for method in methods_to_test:
                print(f"\n{'='*20} {method['name']} {'='*20}")

                # 执行增强版RAG查询
                results = rag_system.enhanced_query(
                    original_query=query,
                    use_rewriting=method['rewriting'],
                    use_hyde=method['hyde'],
                    use_rrf=method['rrf'],
                    top_k=10,
                    rrf_k=3
                )

                print(f"检索方法: {results.get('retrieval_method', 'Unknown')}")
                print("-" * 50)

            print("\n" + "=" * 100)

    if mode in ["2", "4"]:
        # 检索评估模式
        print("\n" + "=" * 80)
        print("检索评估模式：评估不同检索方法的性能")
        print("=" * 80)

        try:
            evaluation_results = rag_system.evaluate_retrieval_methods()

            # 保存评估结果
            results_file = 'local_data/retrieval_evaluation_results.json'
            os.makedirs('local_data', exist_ok=True)
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(evaluation_results, f, ensure_ascii=False, indent=2, default=str)

            print(f"\n检索评估结果已保存到: {results_file}")

        except Exception as e:
            print(f"检索评估过程中出错: {e}")

    if mode in ["3", "4"]:
        # 端到端评估模式
        print("\n" + "=" * 80)
        print("端到端评估模式：评估检索+生成的完整流程")
        print("=" * 80)

        try:
            end_to_end_results = rag_system.evaluate_end_to_end()

            # 保存端到端评估结果
            results_file = 'local_data/end_to_end_evaluation_results.json'
            os.makedirs('local_data', exist_ok=True)
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(end_to_end_results, f, ensure_ascii=False, indent=2, default=str)

            print(f"\n端到端评估结果已保存到: {results_file}")

        except Exception as e:
            print(f"端到端评估过程中出错: {e}")

    print("\n" + "=" * 80)
    print("程序执行完成！")
    print("=" * 80)


if __name__ == "__main__":
    main()

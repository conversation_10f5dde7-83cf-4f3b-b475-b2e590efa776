"""
依赖修复脚本

该脚本用于检查和安装RAG系统所需的依赖包，
特别是解决unstructured[md]缺失导致的BM25初始化问题。
"""

import subprocess
import sys
import importlib

def check_package(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def test_unstructured_md():
    """测试unstructured[md]功能"""
    try:
        from unstructured.partition.md import partition_md
        print("✅ unstructured[md] 功能可用")
        return True
    except ImportError as e:
        print(f"❌ unstructured[md] 功能不可用: {e}")
        return False

def test_nltk_data():
    """测试NLTK数据"""
    try:
        import nltk
        
        # 检查punkt
        try:
            nltk.data.find('tokenizers/punkt')
            print("✅ NLTK punkt 数据可用")
        except LookupError:
            print("⚠️ NLTK punkt 数据缺失，正在下载...")
            nltk.download('punkt', quiet=True)
            print("✅ NLTK punkt 数据下载完成")
        
        # 检查stopwords
        try:
            nltk.data.find('corpora/stopwords')
            print("✅ NLTK stopwords 数据可用")
        except LookupError:
            print("⚠️ NLTK stopwords 数据缺失，正在下载...")
            nltk.download('stopwords', quiet=True)
            print("✅ NLTK stopwords 数据下载完成")
        
        return True
    except ImportError:
        print("❌ NLTK 未安装")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("RAG系统依赖检查和修复")
    print("=" * 60)
    
    # 检查基本依赖
    basic_packages = [
        "unstructured",
        "nltk",
        "sklearn",
        "numpy",
        "requests",
        "python-dotenv"
    ]
    
    print("\n📋 检查基本依赖...")
    missing_packages = []
    for package in basic_packages:
        if check_package(package):
            print(f"✅ {package}")
        else:
            print(f"❌ {package} - 缺失")
            missing_packages.append(package)
    
    # 安装缺失的基本依赖
    if missing_packages:
        print(f"\n🔧 安装缺失的基本依赖: {missing_packages}")
        for package in missing_packages:
            install_package(package)
    
    # 检查和安装unstructured[md]
    print("\n📋 检查unstructured[md]依赖...")
    if not test_unstructured_md():
        print("🔧 安装unstructured[md]...")
        if install_package('"unstructured[md]"'):
            test_unstructured_md()
    
    # 检查NLTK数据
    print("\n📋 检查NLTK数据...")
    test_nltk_data()
    
    # 可选依赖检查
    print("\n📋 检查可选依赖...")
    optional_packages = {
        "rouge_score": "ROUGE评分功能",
        "camel": "CAMEL框架"
    }
    
    for package, description in optional_packages.items():
        if check_package(package):
            print(f"✅ {package} - {description}")
        else:
            print(f"⚠️ {package} - {description} (可选)")
    
    # 最终测试
    print("\n🧪 最终功能测试...")
    
    # 测试文档解析
    try:
        from unstructured.partition.auto import partition
        print("✅ 文档解析功能可用")
    except ImportError:
        print("❌ 文档解析功能不可用")
    
    # 测试BM25相关功能
    try:
        import nltk
        from nltk.tokenize import word_tokenize
        from nltk.corpus import stopwords
        
        # 简单测试
        tokens = word_tokenize("This is a test.")
        stop_words = stopwords.words('english')
        print("✅ BM25相关功能可用")
    except Exception as e:
        print(f"❌ BM25相关功能不可用: {e}")
    
    print("\n" + "=" * 60)
    print("依赖检查完成！")
    print("=" * 60)
    
    print("\n💡 如果仍有问题，请尝试以下命令:")
    print('pip install "unstructured[md]"')
    print('pip install nltk')
    print('python -c "import nltk; nltk.download(\'punkt\'); nltk.download(\'stopwords\')"')

def quick_fix():
    """快速修复常见问题"""
    print("🚀 快速修复模式")
    print("-" * 30)
    
    # 安装关键依赖
    critical_packages = [
        '"unstructured[md]"',
        'nltk'
    ]
    
    for package in critical_packages:
        install_package(package)
    
    # 下载NLTK数据
    try:
        import nltk
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        print("✅ NLTK数据下载完成")
    except:
        print("❌ NLTK数据下载失败")
    
    print("✅ 快速修复完成")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="RAG系统依赖修复工具")
    parser.add_argument("--quick", action="store_true", help="快速修复模式")
    
    args = parser.parse_args()
    
    if args.quick:
        quick_fix()
    else:
        main()

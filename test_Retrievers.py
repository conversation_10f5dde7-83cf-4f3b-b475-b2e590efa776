#初始化 VectorRetriever
#可以选择传入一个嵌入模型，如果不提供嵌入模型，默认会使用 OpenAIEmbedding。

from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever

embedding_model=SentenceTransformerEncoder(model_name='intfloat/e5-large-v2')


# 指定内容来源路径，可以是文件路径或 URL
content_input_path = "https://www.camel-ai.org/"

# 创建或初始化向量存储（例如 QdrantStorage）
from camel.storages.vectordb_storages import QdrantStorage

vector_storage = QdrantStorage(
    vector_dim=embedding_model.get_output_dim(),  # 嵌入向量的维度
    collection_name="my first collection",          # 向量存储的集合名称
    path="storage_customized_run",                  # 向量存储的位置
)
# 初始化 VectorRetriever
vr = VectorRetriever(embedding_model=embedding_model,storage=vector_storage)
# 将内容嵌入并存储到向量存储中
vr.process(content_input_path, chunk_type="chunk_by_title")

# 指定查询字符串
query = "What is CAMEL"

# 执行查询并检索结果
results = vr.query(query)

# 打印检索结果
print(results)
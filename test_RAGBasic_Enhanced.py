"""
基于原有RAGBasic的增强版本：集成Rewriting和HyDE

这个脚本在原有的test_RAGBasic.py基础上，添加了：
1. 查询重写功能：修正用户输入的错别字和表达问题
2. HyDE (Hypothetical Document Embeddings)：生成假设文档来改善检索效果

HyDE方法说明：
- 论文「Precise Zero-Shot Dense Retrieval without Relevance Labels」提出
- 使用LLM为用户查询生成假设文档
- 假设文档虽然可能包含错误，但与知识库中的真实文档相关联
- 通过假设文档的向量表示来检索相似的真实文档，提高检索准确性
"""

import os
import requests

# ===== 原有的知识库设置部分 =====
os.makedirs('local_data', exist_ok=True)

url = "https://arxiv.org/pdf/2303.17760.pdf"
response = requests.get(url)
with open('local_data/camel_paper.pdf', 'wb') as file:
    file.write(response.content)

from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever

# 初始化嵌入模型
embedding_model = SentenceTransformerEncoder(model_name='intfloat/e5-large-v2')

# 创建向量数据库
from camel.storages.vectordb_storages import QdrantStorage

vector_storage = QdrantStorage(
    vector_dim=embedding_model.get_output_dim(),
    collection="demo_collection",
    path="storage_customized_run",
    collection_name="论文"
)

# 初始化向量检索器
vr = VectorRetriever(embedding_model=embedding_model, storage=vector_storage)

# 处理文档并建立向量数据库
vr.process(content="local_data/camel_paper.pdf")

# ===== 新增：LLM模型和代理设置 =====
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 创建LLM模型
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

# ===== 新增功能1：查询重写代理 =====
def create_rewriting_agent():
    """创建查询重写代理"""
    rewriting_sys_msg = """
    你是RAG模块中的Rewriting助手，目的是理解用户的提问，并且重新组织和优化用户的提问表达，
    修正用户输入中可能存在的错别字的情况并重构提问来使得句子表达更加通顺严谨。
    请直接输出重写后的查询，不要添加额外的解释。
    """
    return ChatAgent(system_message=rewriting_sys_msg, model=model)

# ===== 新增功能2：HyDE假设文档生成代理 =====
def create_hyde_agent():
    """创建HyDE假设文档生成代理"""
    hyde_sys_msg = """
    你是一个专门生成假设文档的助手。根据用户的查询，生成一个相关的假设文档片段。
    这个文档应该：
    1. 直接回答用户的问题
    2. 包含相关的技术细节和概念
    3. 使用专业但清晰的语言
    4. 长度适中（200-400字）
    
    请直接输出假设文档内容，不要添加额外的解释或格式。
    """
    return ChatAgent(system_message=hyde_sys_msg, model=model)

# ===== 新增功能3：RAG回答生成代理 =====
def create_rag_agent():
    """创建RAG回答生成代理"""
    rag_sys_msg = """
    你是一个帮助回答问题的助手。
    我会给你原始查询和检索到的上下文信息。
    请根据检索到的上下文回答原始查询。
    如果上下文信息不足以回答问题，请说"根据提供的信息，我无法完全回答这个问题"。
    请确保回答准确、完整且有条理。
    """
    return ChatAgent(system_message=rag_sys_msg, model=model)

# 创建各种代理
rewriting_agent = create_rewriting_agent()
hyde_agent = create_hyde_agent()
rag_agent = create_rag_agent()

# ===== 新增功能4：查询重写函数 =====
def rewrite_query(original_query):
    """
    重写用户查询，修正错别字和改善表达
    
    Args:
        original_query: 原始用户查询
        
    Returns:
        重写后的查询
    """
    print(f"原始查询: {original_query}")
    
    rewrite_prompt = f"用户的原始提问如下：{original_query}"
    response = rewriting_agent.step(rewrite_prompt)
    rewritten_query = response.msgs[0].content.strip()
    
    print(f"重写查询: {rewritten_query}")
    return rewritten_query

# ===== 新增功能5：HyDE假设文档生成函数 =====
def generate_hypothetical_document(query):
    """
    生成假设文档 (HyDE方法)
    
    Args:
        query: 用户查询
        
    Returns:
        生成的假设文档
    """
    print(f"为查询生成假设文档: {query}")
    
    hyde_prompt = f"请为以下查询生成一个相关的假设文档：{query}"
    response = hyde_agent.step(hyde_prompt)
    hypothetical_doc = response.msgs[0].content.strip()
    
    print(f"生成的假设文档:\n{hypothetical_doc}\n")
    return hypothetical_doc

# ===== 新增功能6：增强版检索函数 =====
def enhanced_retrieve(query, use_hyde=True, top_k=3):
    """
    增强版检索函数，支持HyDE方法
    
    Args:
        query: 用户查询
        use_hyde: 是否使用HyDE方法
        top_k: 返回的文档数量
        
    Returns:
        检索到的文档列表
    """
    if use_hyde:
        # 使用HyDE方法：先生成假设文档，再用假设文档检索
        hypothetical_doc = generate_hypothetical_document(query)
        results = vr.query(query=hypothetical_doc, top_k=top_k)
        print(f"HyDE检索结果 (top_{top_k}):")
    else:
        # 传统方法：直接用原始查询检索
        results = vr.query(query=query, top_k=top_k)
        print(f"传统检索结果 (top_{top_k}):")
    
    for i, result in enumerate(results):
        print(f"文档 {i+1}: {result['text'][:150]}...")
    
    return results

# ===== 新增功能7：完整的增强RAG流程 =====
def enhanced_rag_pipeline(original_query, use_rewriting=True, use_hyde=True, top_k=2):
    """
    完整的增强RAG流程
    
    Args:
        original_query: 原始用户查询
        use_rewriting: 是否使用查询重写
        use_hyde: 是否使用HyDE方法
        top_k: 检索文档数量
        
    Returns:
        最终答案
    """
    print("=" * 60)
    print("开始增强RAG流程")
    print("=" * 60)
    
    # 步骤1: 查询重写（可选）
    if use_rewriting:
        print("\n步骤1: 查询重写")
        print("-" * 30)
        processed_query = rewrite_query(original_query)
    else:
        processed_query = original_query
    
    # 步骤2: 增强检索
    print(f"\n步骤2: 文档检索")
    print("-" * 30)
    retrieved_docs = enhanced_retrieve(processed_query, use_hyde=use_hyde, top_k=top_k)
    
    # 步骤3: 生成答案
    print(f"\n步骤3: 生成答案")
    print("-" * 30)
    
    # 构建上下文
    context = "\n\n".join([doc['text'] for doc in retrieved_docs])
    
    # 构建提示
    prompt = f"""
    原始查询: {original_query}
    处理后查询: {processed_query}
    
    检索到的上下文信息:
    {context}
    
    请根据上述上下文信息回答原始查询。
    """
    
    response = rag_agent.step(prompt)
    final_answer = response.msgs[0].content.strip()
    
    print(f"最终答案:\n{final_answer}")
    
    return final_answer

# ===== 测试和演示 =====
if __name__ == "__main__":
    print("增强版RAG系统演示")
    print("=" * 60)
    
    # 测试1: 原有的基础查询
    print("\n测试1: 基础查询")
    basic_query = "CAMEL是什么"
    basic_results = vr.query(query=basic_query, top_k=1)
    print(f"查询: {basic_query}")
    print(f"结果: {basic_results[0]['text'][:200]}...")
    
    # 测试2: 包含错别字的查询 + 查询重写
    print("\n\n测试2: 查询重写功能")
    error_query = "我盖如何解决CAMEL中文档冲服的问题问题呢"
    enhanced_rag_pipeline(error_query, use_rewriting=True, use_hyde=False)
    
    # 测试3: HyDE方法测试
    print("\n\n测试3: HyDE方法测试")
    hyde_query = "CAMEL框架的角色扮演机制是如何工作的"
    enhanced_rag_pipeline(hyde_query, use_rewriting=False, use_hyde=True)
    
    # 测试4: 完整增强流程（Rewriting + HyDE）
    print("\n\n测试4: 完整增强流程 (Rewriting + HyDE)")
    full_query = "CAMEL系统中的多智能体协作是怎么实现的呀？"
    enhanced_rag_pipeline(full_query, use_rewriting=True, use_hyde=True)
    
    # 测试5: 对比不同方法的效果
    print("\n\n测试5: 方法对比")
    comparison_query = "什么是角色扮演"
    
    print("方法1: 传统RAG")
    traditional_results = vr.query(query=comparison_query, top_k=1)
    print(f"检索结果: {traditional_results[0]['text'][:200]}...")
    
    print("\n方法2: HyDE增强RAG")
    hyde_results = enhanced_retrieve(comparison_query, use_hyde=True, top_k=1)
    
    print("\n完成所有测试！")

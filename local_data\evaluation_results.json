{"传统向量检索": {"individual_results": [{"query": "什么是CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.13917087301636047, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract"], "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"], "threshold": 0.3}, {"query": "如何开始使用CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.10789078169839394, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract"], "expected_answers": ["首先安装框架：`pip install camel-ai`，然后引入必要的模块。"], "threshold": 0.3}, {"query": "CAMEL AI 的主要特点是什么？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution."], "expected_answers": ["模块化设计、易用性和扩展性。"], "threshold": 0.3}, {"query": "CAMEL AI支持哪些功能？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract"], "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"], "threshold": 0.3}, {"query": "角色扮演功能如何实现？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Option 2:", "Option 2:", "Option 2:"], "expected_answers": ["通过定义角色特征和行为模式、设置对话上下文和约束、实现角色间的交互逻辑、维护角色状态和记忆来实现。"], "threshold": 0.3}], "overall_metrics": {"average_precision": 0.0, "average_recall": 0.0, "average_f1": 0.0, "average_similarity": 0.04941233094295088}, "threshold": 0.3, "num_queries": 5}, "HyDE检索": {"individual_results": [{"query": "什么是CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.03340392102902291, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"], "threshold": 0.3}, {"query": "如何开始使用CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.026655742788148067, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["首先安装框架：`pip install camel-ai`，然后引入必要的模块。"], "threshold": 0.3}, {"query": "CAMEL AI 的主要特点是什么？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["模块化设计、易用性和扩展性。"], "threshold": 0.3}, {"query": "CAMEL AI支持哪些功能？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"], "threshold": 0.3}, {"query": "角色扮演功能如何实现？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["User System Prompt: Never forget you are a <USER_ROLE> and I am a <ASSISTANT_ROLE>. Never flip roles! You will always instruct me. We share a common interest in collaborating to successfully complete a task. I must help you to complete the task. Here is the task: <TASK>. Never forget our task! You must instruct me based on my expertise and your needs to complete the task ONLY in the following two ways:", "User System Prompt: Never forget you are a <USER_ROLE> and I am a <ASSISTANT_ROLE>. Never flip roles! You will always instruct me. We share a common interest in collaborating to successfully complete a task. I must help you to complete the task. Here is the task: <TASK>. Never forget our task! You must instruct me based on my expertise and your needs to complete the task ONLY in the following two ways:", "User System Prompt: Never forget you are a <USER_ROLE> and I am a <ASSISTANT_ROLE>. Never flip roles! You will always instruct me. We share a common interest in collaborating to successfully complete a task. I must help you to complete the task. Here is the task: <TASK>. Never forget our task! You must instruct me based on my expertise and your needs to complete the task ONLY in the following two ways:"], "expected_answers": ["通过定义角色特征和行为模式、设置对话上下文和约束、实现角色间的交互逻辑、维护角色状态和记忆来实现。"], "threshold": 0.3}], "overall_metrics": {"average_precision": 0.0, "average_recall": 0.0, "average_f1": 0.0, "average_similarity": 0.012011932763434197}, "threshold": 0.3, "num_queries": 5}, "RRF检索": {"individual_results": [{"query": "什么是CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.06796542918353103, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"], "threshold": 0.3}, {"query": "如何开始使用CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.05318017555147828, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["首先安装框架：`pip install camel-ai`，然后引入必要的模块。"], "threshold": 0.3}, {"query": "CAMEL AI 的主要特点是什么？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["模块化设计、易用性和扩展性。"], "threshold": 0.3}, {"query": "CAMEL AI支持哪些功能？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"], "threshold": 0.3}, {"query": "角色扮演功能如何实现？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Option 2:", "Option 1:", "You must give me one instruction at a time. I must write a response that appropriately completes the requested instruction. I must decline your instruction honestly if I cannot perform the instruction due to physical, moral, legal reasons or my capability and explain the reasons. You should instruct me not ask me questions. Now you must start to instruct me using the two ways described above. Do not add anything else other than your instruction and the optional corresponding input! Keep giving"], "expected_answers": ["通过定义角色特征和行为模式、设置对话上下文和约束、实现角色间的交互逻辑、维护角色状态和记忆来实现。"], "threshold": 0.3}], "overall_metrics": {"average_precision": 0.0, "average_recall": 0.0, "average_f1": 0.0, "average_similarity": 0.024229120947001866}, "threshold": 0.3, "num_queries": 5}, "HyDE+RRF检索": {"individual_results": [{"query": "什么是CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.06660492746647174, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Fair evaluation: Please evaluate the messages solely based on their content and performance for the specific task. Avoid any biases or external influences when making your choice.\n\nMultiple evaluations: You may receive different tasks during the evaluation process. Each task will present two messages, one from CAMEL and one from ChatGPT. Please evaluate each task independently based on the given guidelines."], "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"], "threshold": 0.3}, {"query": "如何开始使用CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.017216581652013643, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "7. Long-term planning: Develop a long-term plan to ensure that the blackout does not result in long-term negative consequences for humanity. This can include conducting research and analysis to identify potential risks and developing strategies to mitigate those risks."], "expected_answers": ["首先安装框架：`pip install camel-ai`，然后引入必要的模块。"], "threshold": 0.3}, {"query": "CAMEL AI 的主要特点是什么？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Fair evaluation: Please evaluate the messages solely based on their content and performance for the specific task. Avoid any biases or external influences when making your choice.\n\nMultiple evaluations: You may receive different tasks during the evaluation process. Each task will present two messages, one from CAMEL and one from ChatGPT. Please evaluate each task independently based on the given guidelines."], "expected_answers": ["模块化设计、易用性和扩展性。"], "threshold": 0.3}, {"query": "CAMEL AI支持哪些功能？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Fair evaluation: Please evaluate the messages solely based on their content and performance for the specific task. Avoid any biases or external influences when making your choice.\n\nMultiple evaluations: You may receive different tasks during the evaluation process. Each task will present two messages, one from CAMEL and one from ChatGPT. Please evaluate each task independently based on the given guidelines."], "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"], "threshold": 0.3}, {"query": "角色扮演功能如何实现？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["User System Prompt: Never forget you are a <USER_ROLE> and I am a <ASSISTANT_ROLE>. Never flip roles! You will always instruct me. We share a common interest in collaborating to successfully complete a task. I must help you to complete the task. Here is the task: <TASK>. Never forget our task! You must instruct me based on my expertise and your needs to complete the task ONLY in the following two ways:", "Assistant System Prompt: Never forget you are a <ASSISTANT_ROLE> and I am a <USER_ROLE>. Never flip roles! Never instruct me! We share a common interest in collaborating to successfully complete a task. You must help me to complete the task. Here is the task: <TASK>. Never forget our task! I must instruct you based on your expertise and my needs to complete the task.", "You can perform a set of actions by calling the available Python functions. You should perform actions based on the descriptions of the functions."], "expected_answers": ["通过定义角色特征和行为模式、设置对话上下文和约束、实现角色间的交互逻辑、维护角色状态和记忆来实现。"], "threshold": 0.3}], "overall_metrics": {"average_precision": 0.0, "average_recall": 0.0, "average_f1": 0.0, "average_similarity": 0.016764301823697078}, "threshold": 0.3, "num_queries": 5}}
{"传统向量检索": {"individual_results": [{"query": "什么是CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.13917087301636047, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract"], "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"], "threshold": 0.7}, {"query": "如何开始使用CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.10789078169839394, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract"], "expected_answers": ["首先安装框架：`pip install camel-ai`，然后引入必要的模块。"], "threshold": 0.7}, {"query": "CAMEL AI 的主要特点是什么？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution."], "expected_answers": ["模块化设计、易用性和扩展性。"], "threshold": 0.7}, {"query": "CAMEL AI支持哪些功能？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract"], "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"], "threshold": 0.7}, {"query": "角色扮演功能如何实现？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Option 2:", "Option 2:", "Option 2:"], "expected_answers": ["通过定义角色特征和行为模式、设置对话上下文和约束、实现角色间的交互逻辑、维护角色状态和记忆来实现。"], "threshold": 0.7}], "overall_metrics": {"average_precision": 0.0, "average_recall": 0.0, "average_f1": 0.0, "average_similarity": 0.04941233094295088}, "threshold": 0.7, "num_queries": 5}, "HyDE检索": {"individual_results": [{"query": "什么是CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.03340392102902291, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"], "threshold": 0.7}, {"query": "如何开始使用CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.026655742788148067, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["首先安装框架：`pip install camel-ai`，然后引入必要的模块。"], "threshold": 0.7}, {"query": "CAMEL AI 的主要特点是什么？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["模块化设计、易用性和扩展性。"], "threshold": 0.7}, {"query": "CAMEL AI支持哪些功能？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"], "threshold": 0.7}, {"query": "角色扮演功能如何实现？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["You must give me one instruction at a time. I must write a response that appropriately completes the requested instruction. I must decline your instruction honestly if I cannot perform the instruction due to physical, moral, legal reasons or my capability and explain the reasons. You should instruct me not ask me questions. Now you must start to instruct me using the two ways described above. Do not add anything else other than your instruction and the optional corresponding input! Keep giving", "You must give me one instruction at a time. I must write a response that appropriately completes the requested instruction. I must decline your instruction honestly if I cannot perform the instruction due to physical, moral, legal reasons or my capability and explain the reasons. You should instruct me not ask me questions. Now you must start to instruct me using the two ways described above. Do not add anything else other than your instruction and the optional corresponding input! Keep giving", "You must give me one instruction at a time. I must write a response that appropriately completes the requested instruction. I must decline your instruction honestly if I cannot perform the instruction due to physical, moral, legal reasons or my capability and explain the reasons. You should instruct me not ask me questions. Now you must start to instruct me using the two ways described above. Do not add anything else other than your instruction and the optional corresponding input! Keep giving"], "expected_answers": ["通过定义角色特征和行为模式、设置对话上下文和约束、实现角色间的交互逻辑、维护角色状态和记忆来实现。"], "threshold": 0.7}], "overall_metrics": {"average_precision": 0.0, "average_recall": 0.0, "average_f1": 0.0, "average_similarity": 0.012011932763434197}, "threshold": 0.7, "num_queries": 5}, "RRF检索": {"individual_results": [{"query": "什么是CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.06796542918353103, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"], "threshold": 0.7}, {"query": "如何开始使用CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.05318017555147828, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["首先安装框架：`pip install camel-ai`，然后引入必要的模块。"], "threshold": 0.7}, {"query": "CAMEL AI 的主要特点是什么？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["模块化设计、易用性和扩展性。"], "threshold": 0.7}, {"query": "CAMEL AI支持哪些功能？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:"], "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"], "threshold": 0.7}, {"query": "角色扮演功能如何实现？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Option 2:", "Option 1:", "You must give me one instruction at a time. I must write a response that appropriately completes the requested instruction. I must decline your instruction honestly if I cannot perform the instruction due to physical, moral, legal reasons or my capability and explain the reasons. You should instruct me not ask me questions. Now you must start to instruct me using the two ways described above. Do not add anything else other than your instruction and the optional corresponding input! Keep giving"], "expected_answers": ["通过定义角色特征和行为模式、设置对话上下文和约束、实现角色间的交互逻辑、维护角色状态和记忆来实现。"], "threshold": 0.7}], "overall_metrics": {"average_precision": 0.0, "average_recall": 0.0, "average_f1": 0.0, "average_similarity": 0.024229120947001866}, "threshold": 0.7, "num_queries": 5}, "HyDE+RRF检索": {"individual_results": [{"query": "什么是CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.06796542918353103, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract"], "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"], "threshold": 0.7}, {"query": "如何开始使用CAMEL AI？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.017216581652013643, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "Objective: Your task is to compare two messages, one generated by CAMEL and the other by ChatGPT. These messages will be displayed anonymously on your screen. • Voting: After reading both messages, please vote for the solution you prefer based on the given task. You can only choose one solution.", "User Message: <CAMEL_TASK_DONE>\n\nAssistant Message: Great! Let me know if you have any other tasks or questions.\n\nAbove we provide an interesting example where a python programmer (assistant) is collaborating with a stock trader (user) on developing a trading bot for the stock market.\n\n17\n\nB Cooperative Role-Playing: The Bad Mind"], "expected_answers": ["首先安装框架：`pip install camel-ai`，然后引入必要的模块。"], "threshold": 0.7}, {"query": "CAMEL AI 的主要特点是什么？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "instruction due to physical, moral, legal reasons or my capability and explain the reasons. You should instruct me not ask me questions. Now you must start to instruct me using the two ways described above. Do not add anything else other than your instruction and the optional corresponding input! Keep giving me instructions and necessary inputs until you think the task is completed. When the task is completed, you must only reply with a single word <CAMEL_TASK_DONE>. Never say <CAMEL_TASK_DONE>"], "expected_answers": ["模块化设计、易用性和扩展性。"], "threshold": 0.7}, {"query": "CAMEL AI支持哪些功能？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["Human Evaluation Guideline\n\nThank you for participating in our task evaluation! We are comparing the performance of ChatGPT and a method called CAMEL in completing tasks. Your input will help us under- stand which solution users prefer for different tasks. Here are the guidelines for this evaluation:", "3 2 0 2\n\nv o N 2\n\n] I\n\nA . s c [\n\n2 v 0 6 7 7 1 . 3 0 3 2 : v i X r a\n\nCAMEL: Communicative Agents for “Mind” Exploration of Large Language Model Society https://www.camel-ai.org\n\nGuo<PERSON>*\n\n<PERSON>i <PERSON>*\n\n<PERSON><PERSON><PERSON><PERSON> Abdullah University of Science and Technology (KAUST)\n\nAbstract", "instruction due to physical, moral, legal reasons or my capability and explain the reasons. You should instruct me not ask me questions. Now you must start to instruct me using the two ways described above. Do not add anything else other than your instruction and the optional corresponding input! Keep giving me instructions and necessary inputs until you think the task is completed. When the task is completed, you must only reply with a single word <CAMEL_TASK_DONE>. Never say <CAMEL_TASK_DONE>"], "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"], "threshold": 0.7}, {"query": "角色扮演功能如何实现？", "precision": 0.0, "recall": 0.0, "f1": 0.0, "avg_similarity": 0.0, "retrieved_texts": ["You must give me one instruction at a time. I must write a response that appropriately completes the requested instruction. I must decline your instruction honestly if I cannot perform the instruction due to physical, moral, legal reasons or my capability and explain the reasons. You should instruct me not ask me questions. Now you must start to instruct me using the two ways described above. Do not add anything else other than your instruction and the optional corresponding input! Keep giving", "Option 2:", "import time\n\ndef mo nit or_ soc ia l_ me dia ( ticker , keyword , available_money ,\n\napi_key , api_secret , access_token , a cce ss_ tok en_secret ) :\n\nwhile True :\n\ncurrent_price , sentiment = get_stock_info ( ticker , keyword\n\n)\n\nsentiment_result = g et _tweets_sentiment ( search_tweets (\n\nkeyword ) )\n\naction , num_shares = execute_trade ( sentiment_result ,\n\ncurrent_price , available_money )\n\nif action == ’ buy ’:\n\ntotal_cost = ca l cula te _trade_cost ( num_shares ,"], "expected_answers": ["通过定义角色特征和行为模式、设置对话上下文和约束、实现角色间的交互逻辑、维护角色状态和记忆来实现。"], "threshold": 0.7}], "overall_metrics": {"average_precision": 0.0, "average_recall": 0.0, "average_f1": 0.0, "average_similarity": 0.017036402167108937}, "threshold": 0.7, "num_queries": 5}}
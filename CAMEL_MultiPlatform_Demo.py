#!/usr/bin/env python3
"""
CAMEL多平台数据源演示
展示如何集成大众点评和微信小程序数据作为信息来源
"""

import json
from typing import Dict, List
from datetime import datetime

# 多平台数据源管理系统
class MultiPlatformDataSource:
    """多平台数据源管理系统"""
    
    def __init__(self):
        # 大众点评数据源模拟
        self.dianping_venues = {
            '北京': {
                '朝阳区': [
                    {
                        'name': '三里屯SOHO攀岩馆',
                        'address': '北京市朝阳区三里屯SOHO',
                        'sports': ['攀岩'],
                        'rating': 4.5,
                        'price_range': '200-400元/次',
                        'reviews': 156,
                        'source': 'dianping',
                        'verified': True,
                        'features': ['专业教练', '设备齐全', '环境优雅'],
                        'opening_hours': '10:00-22:00',
                        'phone': '010-8888-1234',
                        'tags': ['初学者友好', '安全保障', '停车便利']
                    },
                    {
                        'name': '工体羽毛球馆',
                        'address': '北京市朝阳区工人体育场',
                        'sports': ['羽毛球'],
                        'rating': 4.3,
                        'price_range': '80-150元/小时',
                        'reviews': 324,
                        'source': 'dianping',
                        'verified': True,
                        'features': ['场地标准', '交通便利', '停车方便'],
                        'opening_hours': '06:00-23:00',
                        'phone': '010-8888-2345',
                        'tags': ['专业场地', '交通便利', '设备完善']
                    },
                    {
                        'name': '朝阳公园健身中心',
                        'address': '北京市朝阳区朝阳公园南路',
                        'sports': ['健身', '跑步', '瑜伽'],
                        'rating': 4.2,
                        'price_range': '150-300元/次',
                        'reviews': 89,
                        'source': 'dianping',
                        'verified': True,
                        'features': ['环境优美', '设备新', '教练专业'],
                        'opening_hours': '06:00-22:00',
                        'phone': '010-8888-3456',
                        'tags': ['环境优美', '设备新', '教练专业']
                    }
                ],
                '海淀区': [
                    {
                        'name': '中关村攀岩俱乐部',
                        'address': '北京市海淀区中关村大街',
                        'sports': ['攀岩', '健身'],
                        'rating': 4.6,
                        'price_range': '180-350元/次',
                        'reviews': 203,
                        'source': 'dianping',
                        'verified': True,
                        'features': ['专业攀岩墙', '安全保障', '初学者友好'],
                        'opening_hours': '09:00-21:00',
                        'phone': '010-8888-4567',
                        'tags': ['专业攀岩', '安全第一', '教学优质']
                    },
                    {
                        'name': '清华游泳馆',
                        'address': '北京市海淀区清华大学',
                        'sports': ['游泳'],
                        'rating': 4.4,
                        'price_range': '50-80元/次',
                        'reviews': 445,
                        'source': 'dianping',
                        'verified': True,
                        'features': ['水质优良', '价格实惠', '环境安静'],
                        'opening_hours': '06:30-21:30',
                        'phone': '010-8888-5678',
                        'tags': ['水质优良', '价格实惠', '学术氛围']
                    }
                ]
            }
        }
        
        # 微信小程序数据源模拟
        self.wechat_miniprogram_venues = {
            '北京': {
                '朝阳区': [
                    {
                        'name': '悦动攀岩馆',
                        'address': '北京市朝阳区望京SOHO',
                        'sports': ['攀岩'],
                        'rating': 4.7,
                        'price_range': '220-380元/次',
                        'reviews': 78,
                        'source': 'wechat_miniprogram',
                        'verified': True,
                        'features': ['在线预约', '会员优惠', '专业指导'],
                        'opening_hours': '10:00-22:00',
                        'miniprogram_name': '悦动运动',
                        'booking_available': True,
                        'membership_discount': '8.5折',
                        'online_payment': True
                    },
                    {
                        'name': '三里屯健身工厂',
                        'address': '北京市朝阳区三里屯太古里',
                        'sports': ['健身', '跑步', '瑜伽'],
                        'rating': 4.5,
                        'price_range': '200-500元/次',
                        'reviews': 167,
                        'source': 'wechat_miniprogram',
                        'verified': True,
                        'features': ['24小时营业', '高端设备', '私教服务'],
                        'opening_hours': '24小时',
                        'miniprogram_name': '健身工厂',
                        'booking_available': True,
                        'membership_discount': '7折',
                        'online_payment': True
                    }
                ],
                '西城区': [
                    {
                        'name': '金融街游泳中心',
                        'address': '北京市西城区金融街',
                        'sports': ['游泳', '健身'],
                        'rating': 4.3,
                        'price_range': '100-200元/次',
                        'reviews': 234,
                        'source': 'wechat_miniprogram',
                        'verified': True,
                        'features': ['恒温泳池', '商务环境', '会员制'],
                        'opening_hours': '06:00-22:00',
                        'miniprogram_name': '金融街运动',
                        'booking_available': True,
                        'membership_discount': '9折',
                        'online_payment': True
                    }
                ]
            }
        }
    
    def search_dianping_venues(self, area: str, sport_type: str) -> List[Dict]:
        """搜索大众点评场馆数据"""
        venues = []
        
        if '北京' in self.dianping_venues and area in self.dianping_venues['北京']:
            area_venues = self.dianping_venues['北京'][area]
            
            for venue in area_venues:
                if sport_type in venue['sports'] or any(sport_type in sport for sport in venue['sports']):
                    venues.append(venue)
        
        return venues
    
    def search_wechat_venues(self, area: str, sport_type: str) -> List[Dict]:
        """搜索微信小程序场馆数据"""
        venues = []
        
        if '北京' in self.wechat_miniprogram_venues and area in self.wechat_miniprogram_venues['北京']:
            area_venues = self.wechat_miniprogram_venues['北京'][area]
            
            for venue in area_venues:
                if sport_type in venue['sports'] or any(sport_type in sport for sport in venue['sports']):
                    venues.append(venue)
        
        return venues
    
    def get_all_platform_venues(self, area: str, sport_type: str) -> Dict[str, List[Dict]]:
        """获取所有平台的场馆数据"""
        return {
            'dianping': self.search_dianping_venues(area, sport_type),
            'wechat': self.search_wechat_venues(area, sport_type)
        }
    
    def format_venue_info(self, venue: Dict) -> str:
        """格式化场馆信息显示"""
        source_icon = {
            'dianping': '🍽️',
            'wechat_miniprogram': '💬'
        }
        
        info = f"""
{source_icon.get(venue['source'], '📍')} **{venue['name']}** ({venue['source'].upper()})
📍 地址：{venue['address']}
⭐ 评分：{venue['rating']}/5.0 ({venue['reviews']}条评价)
💰 价格：{venue['price_range']}
⏰ 营业时间：{venue['opening_hours']}
🏃 运动类型：{', '.join(venue['sports'])}
✨ 特色：{', '.join(venue['features'])}
"""
        
        # 添加微信小程序特有信息
        if venue['source'] == 'wechat_miniprogram':
            info += f"📱 小程序：{venue['miniprogram_name']}\n"
            info += f"📅 在线预约：{'✅' if venue.get('booking_available') else '❌'}\n"
            info += f"💳 在线支付：{'✅' if venue.get('online_payment') else '❌'}\n"
            info += f"🎫 会员优惠：{venue.get('membership_discount', '无')}\n"
        
        # 添加大众点评特有信息
        if venue['source'] == 'dianping':
            info += f"📞 电话：{venue.get('phone', '请查询')}\n"
            info += f"🏷️ 标签：{', '.join(venue.get('tags', []))}\n"
        
        return info

def demo_multiplatform_search():
    """演示多平台搜索功能"""
    print("🔍 CAMEL多平台数据源演示")
    print("=" * 60)
    
    # 创建多平台数据源实例
    multi_platform = MultiPlatformDataSource()
    
    # 测试场景
    test_cases = [
        ("朝阳区", "攀岩"),
        ("朝阳区", "健身"),
        ("海淀区", "游泳"),
        ("西城区", "游泳")
    ]
    
    for area, sport in test_cases:
        print(f"\n🔍 搜索：{area} + {sport}")
        print("-" * 40)
        
        # 获取所有平台数据
        all_venues = multi_platform.get_all_platform_venues(area, sport)
        
        # 显示大众点评结果
        dianping_venues = all_venues['dianping']
        if dianping_venues:
            print(f"🍽️ 大众点评找到 {len(dianping_venues)} 个场馆：")
            for venue in dianping_venues:
                print(multi_platform.format_venue_info(venue))
        else:
            print("🍽️ 大众点评：无相关场馆")
        
        # 显示微信小程序结果
        wechat_venues = all_venues['wechat']
        if wechat_venues:
            print(f"💬 微信小程序找到 {len(wechat_venues)} 个场馆：")
            for venue in wechat_venues:
                print(multi_platform.format_venue_info(venue))
        else:
            print("💬 微信小程序：无相关场馆")
        
        # 统计信息
        total_venues = len(dianping_venues) + len(wechat_venues)
        print(f"📊 总计：{total_venues} 个场馆")
        
        if total_venues > 0:
            avg_rating = sum([v['rating'] for v in dianping_venues + wechat_venues]) / total_venues
            total_reviews = sum([v['reviews'] for v in dianping_venues + wechat_venues])
            print(f"📈 平均评分：{avg_rating:.1f}/5.0")
            print(f"📝 总评价数：{total_reviews} 条")

def demo_data_source_comparison():
    """演示不同数据源的特点对比"""
    print("\n📊 数据源特点对比")
    print("=" * 60)
    
    comparison = {
        "大众点评": {
            "优势": [
                "用户评价丰富真实",
                "评分系统成熟",
                "商户信息详细",
                "用户点评质量高"
            ],
            "特色功能": [
                "用户评分和评价",
                "商户详细信息",
                "价格区间明确",
                "联系电话提供"
            ],
            "适用场景": [
                "需要了解用户真实评价",
                "对场馆质量要求较高",
                "希望获得详细商户信息"
            ]
        },
        "微信小程序": {
            "优势": [
                "在线预约便利",
                "支付功能完善",
                "会员体系完整",
                "使用体验流畅"
            ],
            "特色功能": [
                "在线预约系统",
                "微信支付集成",
                "会员优惠政策",
                "小程序生态"
            ],
            "适用场景": [
                "需要在线预约服务",
                "希望享受会员优惠",
                "偏好移动端操作"
            ]
        }
    }
    
    for platform, info in comparison.items():
        print(f"\n🔍 {platform}")
        print("-" * 30)
        
        print("✅ 优势：")
        for advantage in info["优势"]:
            print(f"  • {advantage}")
        
        print("🛠️ 特色功能：")
        for feature in info["特色功能"]:
            print(f"  • {feature}")
        
        print("🎯 适用场景：")
        for scenario in info["适用场景"]:
            print(f"  • {scenario}")

def demo_integration_benefits():
    """演示多平台集成的好处"""
    print("\n🎯 多平台集成的价值")
    print("=" * 60)
    
    benefits = {
        "数据丰富性": [
            "多个数据源提供更全面的场馆信息",
            "不同平台的数据可以相互验证",
            "减少单一数据源的局限性"
        ],
        "用户体验": [
            "用户可以选择最适合的预约方式",
            "提供多种支付和优惠选项",
            "满足不同用户的使用习惯"
        ],
        "信息可靠性": [
            "多平台数据交叉验证",
            "降低虚假信息的风险",
            "提高推荐结果的准确性"
        ],
        "功能完整性": [
            "评价系统 + 预约系统",
            "信息查询 + 在线服务",
            "传统渠道 + 新兴平台"
        ]
    }
    
    for category, items in benefits.items():
        print(f"\n📈 {category}")
        print("-" * 20)
        for item in items:
            print(f"  ✅ {item}")

if __name__ == "__main__":
    # 运行演示
    demo_multiplatform_search()
    demo_data_source_comparison()
    demo_integration_benefits()
    
    print(f"\n🎉 多平台数据源演示完成！")
    print(f"⏰ 演示时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
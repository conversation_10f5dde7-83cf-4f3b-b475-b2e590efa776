from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from camel.messages import BaseMessage

from dotenv import load_dotenv
import os

load_dotenv()

load_dotenv(dotenv_path='.env')  # 确保.env文件存在且路径正确
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-VL-72B-Instruct",
    api_key=api_key,
    url=os.environ.get("OPENAI_COMPATIBILIY_API_BASE_URL", "https://api-inference.modelscope.cn/v1/"),  # 环境变量优先，不存在则使用默认值
    model_config_dict={"temperature": 0.4, "max_tokens": 4096},
)

# 创建代理
agent = ChatAgent(
    model=model,
    output_language='中文'
)

# 读取本地视频文件
video_path = "010e5aa80d13efde6a41dbb5f66c1a32.mp4"
with open(video_path, "rb") as video_file:
    video_bytes = video_file.read()

# 创建包含视频的用户消息
user_msg = BaseMessage.make_user_message(
    role_name="User", 
    content="请描述这段视频的内容", 
    video_bytes=video_bytes  # 将视频字节作为参数传入
)

# 获取模型响应
response = agent.step(user_msg)
print(response.msgs[0].content)


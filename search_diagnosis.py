#!/usr/bin/env python3
"""
搜索诊断脚本 - 分析CAMEL Workforce搜索系统的有效性
"""

import json
from CAMEL_Prompt_task2_workforce import VenueVerificationSystem, GeographicExpansionManager

def diagnose_search_system():
    """诊断搜索系统的各个组件"""
    print("🔍 CAMEL Workforce 搜索系统诊断")
    print("=" * 60)
    
    # 1. 验证系统诊断
    print("\n📊 1. 验证系统诊断")
    print("-" * 40)
    
    verification_system = VenueVerificationSystem()
    
    # 检查数据库内容
    print("已验证场馆数据库内容：")
    for city, areas in verification_system.verified_venues.items():
        print(f"\n🏙️ {city}:")
        for area, venues in areas.items():
            print(f"  📍 {area}: {len(venues)}个场馆")
            for venue in venues:
                print(f"    🏟️ {venue['name']} - 支持运动: {venue['sports']}")
    
    # 2. 运动类型匹配测试
    print(f"\n📊 2. 运动类型匹配测试")
    print("-" * 40)
    
    test_cases = [
        ("朝阳区", "跑步"),
        ("朝阳区", "篮球"),
        ("朝阳区", "游泳"),
        ("西城区", "游泳"),
        ("石景山区", "乒乓球"),
        ("海淀区", "羽毛球")
    ]
    
    for area, sport in test_cases:
        venues = verification_system.get_real_venues_in_area(area, sport)
        print(f"🔍 {area} + {sport}: 找到 {len(venues)} 个场馆")
        for venue in venues:
            print(f"  ✅ {venue['name']}")
        if not venues:
            print(f"  ❌ 无匹配场馆")
    
    # 3. 地理扩展测试
    print(f"\n📊 3. 地理扩展测试")
    print("-" * 40)
    
    geo_manager = GeographicExpansionManager()
    
    test_areas = ["朝阳区", "西城区", "东城区"]
    for area in test_areas:
        print(f"\n🗺️ {area} 扩展测试:")
        
        # 级别1扩展
        level1_areas = geo_manager.get_expansion_areas(area, expansion_level=1)
        print(f"  级别1扩展: {level1_areas}")
        
        # 级别2扩展
        level2_areas = geo_manager.get_expansion_areas(area, expansion_level=2)
        print(f"  级别2扩展: {level2_areas}")
    
    # 4. 运动类型覆盖分析
    print(f"\n📊 4. 运动类型覆盖分析")
    print("-" * 40)
    
    all_sports = set()
    for areas in verification_system.verified_venues['北京'].values():
        for venue in areas:
            all_sports.update(venue['sports'])
    
    print(f"数据库支持的运动类型: {sorted(all_sports)}")
    
    # 检查常见运动类型的覆盖情况
    common_sports = ["跑步", "健身", "游泳", "篮球", "羽毛球", "乒乓球", "足球", "网球", "瑜伽"]
    print(f"\n常见运动类型覆盖情况:")
    for sport in common_sports:
        total_venues = 0
        for area_venues in verification_system.verified_venues['北京'].values():
            for venue in area_venues:
                if sport in venue['sports']:
                    total_venues += 1
        
        status = "✅" if total_venues > 0 else "❌"
        print(f"  {status} {sport}: {total_venues} 个场馆")
    
    # 5. 搜索结果分析建议
    print(f"\n📊 5. 问题分析和建议")
    print("-" * 40)
    
    print("🔍 发现的问题:")
    
    # 检查跑步场馆
    running_venues = 0
    for area_venues in verification_system.verified_venues['北京'].values():
        for venue in area_venues:
            if "跑步" in venue['sports']:
                running_venues += 1
    
    if running_venues == 0:
        print("  ❌ 数据库中没有专门的跑步场馆")
        print("  💡 建议: 添加跑步场馆或将健身房/体育中心标记为支持跑步")
    
    # 检查健身场馆
    fitness_venues = 0
    for area_venues in verification_system.verified_venues['北京'].values():
        for venue in area_venues:
            if "健身" in venue['sports']:
                fitness_venues += 1
    
    print(f"\n💡 改进建议:")
    print("  1. 扩展运动类型标签 - 很多体育中心实际支持跑步")
    print("  2. 添加更多专业场馆数据")
    print("  3. 实现模糊匹配 - 跑步可以匹配健身房/体育中心")
    print("  4. 增加在线搜索结果的解析和验证")

if __name__ == "__main__":
    diagnose_search_system()
"""
RAG查询重写测试脚本

该脚本用于测试RAG（检索增强生成）系统中的查询重写功能。
主要功能是接收用户的原始查询，通过AI模型对查询进行优化和重写，
修正错别字并提高查询的准确性和表达质量。
"""

# 用户的原始查询（包含错别字和表达不清晰的问题）
# 注意：这里的查询包含了一些错别字，如"盖"应为"该"，"冲服"应为"冲突"
original_query = "我盖如何解决CAMEL中文档冲服的问题问题呢，几个版本的文档可能存在代码结构的冲突"

# 系统消息：定义AI助手的角色和任务
# 该消息告诉AI它是一个专门用于查询重写的助手
sys_msg = '你是RAG模块中的Rewriting助手，目的是理解用户的提问，并且重新组织和优化用户的提问表达，修正用户输入中可能存在的错别字的情况并重构提问来使得句子表达更加通顺严谨'

# 导入CAMEL框架相关模块
from camel.models import ModelFactory          # 模型工厂，用于创建不同类型的模型
from camel.types import ModelPlatformType      # 模型平台类型的枚举
from camel.agents import ChatAgent             # 聊天代理类

# 导入环境变量和操作系统相关模块
from dotenv import load_dotenv  # 用于加载.env文件中的环境变量
import os                       # 操作系统接口模块

# 加载环境变量文件(.env)
load_dotenv()

# 从环境变量中获取ModelScope的API密钥
# ModelScope是阿里云推出的机器学习模型平台
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 创建AI模型实例
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,  # 使用OpenAI兼容的模型平台
    model_type="Qwen/Qwen2.5-72B-Instruct",                   # 指定具体的模型类型
    url='https://api-inference.modelscope.cn/v1/',             # ModelScope API的推理端点
    api_key=api_key                                            # API密钥用于身份验证
)

# 创建聊天代理
# 将系统消息和模型绑定到代理上
agent = ChatAgent(system_message=sys_msg, model=model)

# 构造用户消息，要求AI对原始查询进行重写
# 这里使用f-string格式化字符串，将原始查询嵌入到提示中
usr_msg = f'用户的原始提问如下：{original_query}，请优化重写并直接输出新的Query。新的Query: '

# 让AI代理处理用户消息并生成响应
response = agent.step(usr_msg)

# 输出AI重写后的查询结果
# response.msgs[0].content 包含了AI生成的重写后的查询
print(response.msgs[0].content)
"""
RAG系统调试版本

专门用于诊断和解决向量存储和BM25检索器的问题
"""

import os
from typing import List, Dict, Any

# 导入CAMEL框架相关模块
from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever, BM25Retriever
from camel.storages.vectordb_storages import QdrantStorage

# 导入环境变量模块
from dotenv import load_dotenv

def create_simple_knowledge_base():
    """创建简单的知识库用于测试"""
    content = """CAMEL AI 是一个开源的、社区驱动的AI框架。

CAMEL AI的主要特点包括：
- 模块化设计
- 易用性
- 扩展性

如何开始使用CAMEL AI：
1. 首先安装框架：pip install camel-ai
2. 然后引入必要的模块：from camel import *

CAMEL AI支持以下功能：
- 多智能体协作系统
- 角色扮演对话
- 任务分解和执行
- 代码生成和调试
- 文档处理和检索

角色扮演功能通过以下方式实现：
- 定义角色特征和行为模式
- 设置对话上下文和约束条件
- 实现角色间的交互逻辑
- 维护角色状态和记忆信息
"""
    
    os.makedirs('local_data', exist_ok=True)
    kb_path = 'local_data/debug_kb.txt'
    
    with open(kb_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✓ 调试知识库已创建: {kb_path}")
    print(f"  文件大小: {len(content)} 字符")
    return kb_path

def debug_vector_retriever():
    """调试向量检索器"""
    print("=" * 60)
    print("调试向量检索器")
    print("=" * 60)
    
    try:
        # 创建知识库
        kb_path = create_simple_knowledge_base()
        
        # 初始化嵌入模型
        print("1. 初始化嵌入模型...")
        embedding_model = SentenceTransformerEncoder(model_name='intfloat/e5-large-v2')
        print(f"   ✓ 嵌入维度: {embedding_model.get_output_dim()}")
        
        # 初始化向量存储
        print("2. 初始化向量存储...")
        vector_storage = QdrantStorage(
            vector_dim=embedding_model.get_output_dim(),
            collection="debug_collection",
            path="storage_debug",
            collection_name="调试集合"
        )
        print("   ✓ 向量存储初始化完成")
        
        # 初始化向量检索器
        print("3. 初始化向量检索器...")
        vr = VectorRetriever(embedding_model=embedding_model, storage=vector_storage)
        print("   ✓ 向量检索器初始化完成")
        
        # 处理文档
        print("4. 处理文档...")
        print(f"   处理文件: {kb_path}")
        vr.process(content=kb_path)
        print("   ✓ 文档处理完成")
        
        # 测试查询
        print("5. 测试查询...")
        test_queries = [
            "什么是CAMEL AI",
            "CAMEL AI的特点",
            "如何安装"
        ]
        
        for query in test_queries:
            print(f"   查询: {query}")
            try:
                results = vr.query(query=query, top_k=2)
                if results:
                    print(f"   ✓ 找到 {len(results)} 个结果")
                    for i, result in enumerate(results):
                        text_preview = result['text'][:100] + "..." if len(result['text']) > 100 else result['text']
                        print(f"     结果 {i+1}: {text_preview}")
                else:
                    print("   ❌ 未找到结果")
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 向量检索器调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_bm25_retriever():
    """调试BM25检索器"""
    print("\n" + "=" * 60)
    print("调试BM25检索器")
    print("=" * 60)
    
    try:
        # 创建知识库
        kb_path = create_simple_knowledge_base()
        
        # 初始化BM25检索器
        print("1. 初始化BM25检索器...")
        bm25r = BM25Retriever()
        print("   ✓ BM25检索器初始化完成")
        
        # 处理文档
        print("2. 处理文档...")
        print(f"   处理文件: {kb_path}")
        bm25r.process(content_input_path=kb_path)
        print("   ✓ 文档处理完成")
        
        # 测试查询
        print("3. 测试查询...")
        test_queries = [
            "什么是CAMEL AI",
            "CAMEL AI的特点", 
            "如何安装"
        ]
        
        for query in test_queries:
            print(f"   查询: {query}")
            try:
                results = bm25r.query(query=query, top_k=2)
                if results:
                    print(f"   ✓ 找到 {len(results)} 个结果")
                    for i, result in enumerate(results):
                        text_preview = result['text'][:100] + "..." if len(result['text']) > 100 else result['text']
                        print(f"     结果 {i+1}: {text_preview}")
                else:
                    print("   ❌ 未找到结果")
            except Exception as e:
                print(f"   ❌ 查询失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ BM25检索器调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_file_processing():
    """调试文件处理"""
    print("\n" + "=" * 60)
    print("调试文件处理")
    print("=" * 60)
    
    # 检查现有文件
    kb_path = 'local_data/camel_ai_matched_kb.md'
    
    if os.path.exists(kb_path):
        print(f"✓ 文件存在: {kb_path}")
        
        # 检查文件内容
        with open(kb_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"  文件大小: {len(content)} 字符")
        print(f"  行数: {len(content.splitlines())}")
        print(f"  前100字符: {content[:100]}...")
        
        # 检查文件编码
        try:
            with open(kb_path, 'r', encoding='utf-8') as f:
                f.read()
            print("  ✓ UTF-8编码正常")
        except UnicodeDecodeError:
            print("  ❌ UTF-8编码有问题")
        
        return kb_path
    else:
        print(f"❌ 文件不存在: {kb_path}")
        return None

def main():
    """主函数"""
    print("=" * 80)
    print("RAG系统调试工具")
    print("=" * 80)
    
    # 检查环境
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    if api_key:
        print("✓ API密钥已设置")
    else:
        print("⚠️ API密钥未设置（调试不需要）")
    
    # 调试文件处理
    kb_path = debug_file_processing()
    
    # 调试向量检索器
    vector_success = debug_vector_retriever()
    
    # 调试BM25检索器
    bm25_success = debug_bm25_retriever()
    
    # 总结
    print("\n" + "=" * 80)
    print("调试总结")
    print("=" * 80)
    print(f"文件处理: {'✓' if kb_path else '❌'}")
    print(f"向量检索器: {'✓' if vector_success else '❌'}")
    print(f"BM25检索器: {'✓' if bm25_success else '❌'}")
    
    if vector_success and bm25_success:
        print("\n🎉 所有组件工作正常！")
        print("问题可能在于:")
        print("1. 文档分块过程")
        print("2. 向量存储配置")
        print("3. 查询处理逻辑")
    else:
        print("\n❌ 发现问题，请检查上述错误信息")
    
    print("\n建议:")
    print("1. 如果向量检索器失败，检查嵌入模型和存储配置")
    print("2. 如果BM25检索器失败，检查文档预处理")
    print("3. 如果都失败，可能是环境或依赖问题")

if __name__ == "__main__":
    main()

"""
增强版RAG系统：集成查询重写(Rewriting)和假设文档嵌入(HyDE)

该脚本实现了一个增强的RAG系统，结合了以下技术：
1. 查询重写(Query Rewriting)：修正用户查询中的错别字和表达问题
2. HyDE (Hypothetical Document Embeddings)：生成假设文档来改善检索效果
3. 传统RAG检索和生成

HyDE方法的核心思想：
- 使用LLM为用户查询生成假设文档
- 这些假设文档虽然可能包含错误，但与知识库中的真实文档相关联
- 通过假设文档的向量表示来检索相似的真实文档，提高检索准确性
"""

import os
import requests
from typing import List, Dict, Any

# 导入CAMEL框架相关模块
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from camel.agents import ChatAgent
from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever
from camel.storages.vectordb_storages import QdrantStorage

# 导入环境变量模块
from dotenv import load_dotenv

class EnhancedRAGSystem:
    """
    增强版RAG系统类
    
    集成了查询重写、HyDE和传统RAG功能
    """
    
    def __init__(self, api_key: str):
        """
        初始化增强版RAG系统
        
        Args:
            api_key: ModelScope API密钥
        """
        self.api_key = api_key
        
        # 初始化嵌入模型
        self.embedding_model = SentenceTransformerEncoder(
            model_name='intfloat/e5-large-v2'
        )
        
        # 初始化向量存储
        self.vector_storage = QdrantStorage(
            vector_dim=self.embedding_model.get_output_dim(),
            collection="enhanced_rag_collection",
            path="storage_enhanced_rag",
            collection_name="增强RAG知识库"
        )
        
        # 初始化向量检索器
        self.vector_retriever = VectorRetriever(
            embedding_model=self.embedding_model,
            storage=self.vector_storage
        )
        
        # 初始化LLM模型
        self.model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
            model_type="Qwen/Qwen2.5-72B-Instruct",
            url='https://api-inference.modelscope.cn/v1/',
            api_key=self.api_key
        )
        
        # 初始化各种代理
        self._init_agents()
    
    def _init_agents(self):
        """初始化不同功能的AI代理"""
        
        # 查询重写代理
        rewriting_sys_msg = """
        你是RAG模块中的Rewriting助手，目的是理解用户的提问，并且重新组织和优化用户的提问表达，
        修正用户输入中可能存在的错别字的情况并重构提问来使得句子表达更加通顺严谨。
        请直接输出重写后的查询，不要添加额外的解释。
        """
        self.rewriting_agent = ChatAgent(
            system_message=rewriting_sys_msg,
            model=self.model
        )
        
        # HyDE假设文档生成代理
        hyde_sys_msg = """
        你是一个专门生成假设文档的助手。根据用户的查询，生成一个相关的假设文档片段。
        这个文档应该：
        1. 直接回答用户的问题
        2. 包含相关的技术细节和概念
        3. 使用专业但清晰的语言
        4. 长度适中（200-400字）
        
        请直接输出假设文档内容，不要添加额外的解释或格式。
        """
        self.hyde_agent = ChatAgent(
            system_message=hyde_sys_msg,
            model=self.model
        )
        
        # RAG回答生成代理
        rag_sys_msg = """
        你是一个帮助回答问题的助手。
        我会给你原始查询和检索到的上下文信息。
        请根据检索到的上下文回答原始查询。
        如果上下文信息不足以回答问题，请说"根据提供的信息，我无法完全回答这个问题"。
        请确保回答准确、完整且有条理。
        """
        self.rag_agent = ChatAgent(
            system_message=rag_sys_msg,
            model=self.model
        )
    
    def setup_knowledge_base(self, pdf_url: str = None, pdf_path: str = None):
        """
        设置知识库
        
        Args:
            pdf_url: PDF文件的URL
            pdf_path: 本地PDF文件路径
        """
        # 创建本地数据目录
        os.makedirs('local_data', exist_ok=True)
        
        if pdf_url:
            # 下载PDF文件
            print("正在下载PDF文件...")
            response = requests.get(pdf_url)
            pdf_path = 'local_data/knowledge_base.pdf'
            with open(pdf_path, 'wb') as file:
                file.write(response.content)
            print(f"PDF文件已下载到: {pdf_path}")
        
        if pdf_path:
            # 处理PDF文件并建立向量数据库
            print("正在处理PDF文件并建立向量数据库...")
            self.vector_retriever.process(content=pdf_path)
            print("知识库设置完成！")
    
    def rewrite_query(self, original_query: str) -> str:
        """
        重写用户查询
        
        Args:
            original_query: 原始用户查询
            
        Returns:
            重写后的查询
        """
        rewrite_prompt = f"用户的原始提问如下：{original_query}"
        response = self.rewriting_agent.step(rewrite_prompt)
        rewritten_query = response.msgs[0].content.strip()
        
        print(f"原始查询: {original_query}")
        print(f"重写查询: {rewritten_query}")
        
        return rewritten_query
    
    def generate_hypothetical_document(self, query: str) -> str:
        """
        生成假设文档 (HyDE方法)
        
        Args:
            query: 用户查询
            
        Returns:
            生成的假设文档
        """
        hyde_prompt = f"请为以下查询生成一个相关的假设文档：{query}"
        response = self.hyde_agent.step(hyde_prompt)
        hypothetical_doc = response.msgs[0].content.strip()
        
        print(f"生成的假设文档:\n{hypothetical_doc}")
        
        return hypothetical_doc
    
    def retrieve_with_hyde(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        使用HyDE方法进行检索
        
        Args:
            query: 用户查询
            top_k: 返回的文档数量
            
        Returns:
            检索到的文档列表
        """
        # 生成假设文档
        hypothetical_doc = self.generate_hypothetical_document(query)
        
        # 使用假设文档进行检索
        hyde_results = self.vector_retriever.query(
            query=hypothetical_doc,
            top_k=top_k
        )
        
        # 同时使用原始查询进行检索作为对比
        original_results = self.vector_retriever.query(
            query=query,
            top_k=top_k
        )
        
        print(f"\n=== HyDE检索结果 ===")
        for i, result in enumerate(hyde_results):
            print(f"文档 {i+1} (相似度: {result.get('similarity', 'N/A')}):")
            print(f"{result['text'][:200]}...\n")
        
        print(f"=== 原始查询检索结果 ===")
        for i, result in enumerate(original_results):
            print(f"文档 {i+1} (相似度: {result.get('similarity', 'N/A')}):")
            print(f"{result['text'][:200]}...\n")
        
        return hyde_results, original_results
    
    def generate_answer(self, query: str, retrieved_docs: List[Dict[str, Any]]) -> str:
        """
        基于检索到的文档生成答案
        
        Args:
            query: 用户查询
            retrieved_docs: 检索到的文档列表
            
        Returns:
            生成的答案
        """
        # 构建上下文
        context = "\n\n".join([doc['text'] for doc in retrieved_docs])
        
        # 构建提示
        prompt = f"""
        原始查询: {query}
        
        检索到的上下文信息:
        {context}
        
        请根据上述上下文信息回答原始查询。
        """
        
        response = self.rag_agent.step(prompt)
        answer = response.msgs[0].content.strip()
        
        return answer
    
    def enhanced_query(self, original_query: str, use_rewriting: bool = True, 
                      use_hyde: bool = True, top_k: int = 3) -> Dict[str, Any]:
        """
        执行增强版RAG查询
        
        Args:
            original_query: 原始用户查询
            use_rewriting: 是否使用查询重写
            use_hyde: 是否使用HyDE方法
            top_k: 检索文档数量
            
        Returns:
            包含各步骤结果的字典
        """
        print("=" * 60)
        print("开始执行增强版RAG查询")
        print("=" * 60)
        
        results = {
            'original_query': original_query,
            'rewritten_query': None,
            'hypothetical_document': None,
            'retrieved_docs': None,
            'final_answer': None
        }
        
        # 步骤1: 查询重写（可选）
        if use_rewriting:
            print("\n步骤1: 查询重写")
            print("-" * 30)
            rewritten_query = self.rewrite_query(original_query)
            results['rewritten_query'] = rewritten_query
            query_to_use = rewritten_query
        else:
            query_to_use = original_query
        
        # 步骤2: 文档检索
        print(f"\n步骤2: 文档检索")
        print("-" * 30)
        
        if use_hyde:
            print("使用HyDE方法进行检索...")
            hyde_results, original_results = self.retrieve_with_hyde(query_to_use, top_k)
            results['retrieved_docs'] = hyde_results
            results['original_retrieval_docs'] = original_results
            retrieved_docs = hyde_results
        else:
            print("使用传统方法进行检索...")
            retrieved_docs = self.vector_retriever.query(query=query_to_use, top_k=top_k)
            results['retrieved_docs'] = retrieved_docs
        
        # 步骤3: 答案生成
        print(f"\n步骤3: 答案生成")
        print("-" * 30)
        final_answer = self.generate_answer(query_to_use, retrieved_docs)
        results['final_answer'] = final_answer
        
        print(f"\n最终答案:\n{final_answer}")
        
        return results


def main():
    """主函数：演示增强版RAG系统的使用"""
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return
    
    # 初始化增强版RAG系统
    print("初始化增强版RAG系统...")
    rag_system = EnhancedRAGSystem(api_key)
    
    # 设置知识库（使用CAMEL论文）
    pdf_url = "https://arxiv.org/pdf/2303.17760.pdf"
    rag_system.setup_knowledge_base(pdf_url=pdf_url)
    
    # 测试查询（包含错别字）
    test_queries = [
        "我盖如何解决CAMEL中文档冲服的问题问题呢，几个版本的文档可能存在代码结构的冲突",
        "CAMEL是什么东东？它有什么特点吗",
        "角色扮演在CAMEL中是怎么实现的"
    ]
    
    for query in test_queries:
        print("\n" + "=" * 80)
        print(f"测试查询: {query}")
        print("=" * 80)
        
        # 执行增强版RAG查询
        results = rag_system.enhanced_query(
            original_query=query,
            use_rewriting=True,
            use_hyde=True,
            top_k=2
        )
        
        print("\n" + "=" * 80)


if __name__ == "__main__":
    main()

from camel.agents import TaskSpecifyAgent
from camel.models import ModelFactory
from camel.prompts import TextPrompt
from camel.types import ModelPlatformType, TaskType
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)
task_specify_agent = TaskSpecifyAgent(
    model=model, task_type=TaskType.AI_SOCIETY,output_language='ch'
)

my_prompt_template = TextPrompt(
    'Here is a task: I\'m a {大学内党委直属的党委学工部门下美工部的一名部员} '
    'and I want to {现在部门需要换届选举，我志愿竞选美工部的部长一职位，现需要面试时的自我介绍}. ' \
    'Help me to make this task more specific.'
)  # 你可以根据需求自定义任何模板

# 创建任务规格化Agent
task_specify_agent = TaskSpecifyAgent(
    model=model, task_specify_prompt=my_prompt_template
)
response = task_specify_agent.run(
    task_prompt="get promotion",
    meta_dict=dict(occupation="Software Engineer"),
)
print(response)


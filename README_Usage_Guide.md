# RAG系统改进版使用指南

## 🚀 快速开始

### 方案1: 使用简化版（推荐）
如果遇到依赖问题，直接使用简化版：

```bash
python test_RAG_Simple_Improved.py
```

**优点**: 
- ✅ 无额外依赖，直接运行
- ✅ 解决了原始评估的核心问题
- ✅ 包含所有主要改进功能

### 方案2: 安装依赖后使用完整版

1. **安装可选依赖**:
```bash
python install_dependencies.py
```

2. **运行完整版**:
```bash
python test_RAG_Enhanced_Improved.py
```

## 📋 文件说明

### 核心文件
| 文件名 | 说明 | 依赖要求 |
|--------|------|----------|
| `test_RAG_Simple_Improved.py` | 简化版改进系统 | 仅基础依赖 |
| `test_RAG_Enhanced_Improved.py` | 完整版改进系统 | 需要额外依赖 |
| `test_RAG_Improvements_Demo.py` | 改进效果演示 | 可选依赖 |

### 辅助文件
| 文件名 | 说明 |
|--------|------|
| `install_dependencies.py` | 依赖安装向导 |
| `RAG_System_Improvements_Summary.md` | 详细改进总结 |
| `README_Usage_Guide.md` | 使用指南（本文件） |

### 原始文件（参考）
| 文件名 | 说明 |
|--------|------|
| `test_RAG_Enhanced_Assess.py` | 原始增强版（含评估） |
| `test_RAG_HyDE_Enhanced.py` | HyDE增强版 |
| `test_RAG_Evaluation_Demo.py` | 评估演示 |

## 🔧 主要改进

### 1. 解决阈值过高问题 ✅
- **问题**: 原始阈值0.7过高，导致所有指标为0
- **解决**: 降低默认阈值到0.3，支持自适应阈值选择
- **效果**: 避免零分情况，提供有意义的评估结果

### 2. 确保知识库匹配 ✅
- **问题**: CAMEL论文与测试用例不匹配
- **解决**: 创建与测试用例完全匹配的知识库
- **效果**: 检索结果与预期答案高度相关

### 3. 改进相似度计算 ✅
- **问题**: TF-IDF无法捕获语义相似性
- **解决**: 集成Sentence-BERT（完整版）或优化TF-IDF（简化版）
- **效果**: 提升短文本和语义匹配准确性

### 4. 多阈值分析 ✅
- **问题**: 单一阈值缺乏灵活性
- **解决**: 支持0.1-0.7范围内的多阈值测试
- **效果**: 自动找到最优阈值设置

### 5. 端到端评估 ✅
- **问题**: 只评估检索，未评估答案生成
- **解决**: 添加答案质量评估（完整版）
- **效果**: 全面评估RAG系统性能

## 📊 预期改进效果

| 指标 | 改进前 | 改进后 | 提升幅度 |
|------|--------|--------|----------|
| 精确率 | 0.000 | 0.300-0.700 | **+30-70%** |
| 召回率 | 0.000 | 0.300-0.700 | **+30-70%** |
| F1分数 | 0.000 | 0.300-0.700 | **+30-70%** |
| 相似度 | 0.049 | 0.200-0.600 | **+15-55%** |

## 🛠️ 环境配置

### 必需环境变量
```bash
# 设置ModelScope API密钥
export MODELSCOPE_SDK_TOKEN="your_api_key_here"

# Windows用户使用:
set MODELSCOPE_SDK_TOKEN=your_api_key_here
```

### 基础依赖（已有）
- camel-ai
- scikit-learn
- numpy
- python-dotenv

### 可选依赖
```bash
# 语义相似度计算
pip install sentence-transformers

# 评估功能
pip install nltk rouge-score

# 可视化功能
pip install matplotlib seaborn
```

## 🎯 使用示例

### 简化版使用
```python
from test_RAG_Simple_Improved import SimpleImprovedRAGSystem

# 初始化系统
rag_system = SimpleImprovedRAGSystem(api_key)

# 设置知识库
rag_system.setup_knowledge_base()

# 执行评估
results = rag_system.comprehensive_evaluation()
```

### 完整版使用
```python
from test_RAG_Enhanced_Improved import ImprovedRAGSystem

# 初始化系统
rag_system = ImprovedRAGSystem(api_key)

# 设置知识库
rag_system.setup_knowledge_base(use_matched_kb=True)

# 执行综合评估
results = rag_system.comprehensive_evaluation()
```

## 📈 评估结果解读

### 关键指标
- **精确率**: 检索结果的相关性（越高越好）
- **召回率**: 相关信息的覆盖度（越高越好）
- **F1分数**: 精确率和召回率的平衡（越高越好）
- **相似度**: 文本匹配程度（越高越好）

### 性能标准
- **优秀**: F1 > 0.7, 相似度 > 0.5
- **良好**: F1 > 0.5, 相似度 > 0.3
- **可接受**: F1 > 0.3, 相似度 > 0.2
- **需改进**: F1 < 0.3, 相似度 < 0.2

## 🔍 故障排除

### 常见问题

#### 1. ModuleNotFoundError
**问题**: 缺少可选依赖包
**解决**: 
- 使用简化版: `python test_RAG_Simple_Improved.py`
- 或安装依赖: `python install_dependencies.py`

#### 2. API密钥错误
**问题**: MODELSCOPE_SDK_TOKEN未设置
**解决**: 
```bash
export MODELSCOPE_SDK_TOKEN="your_api_key"
```

#### 3. 内存不足
**问题**: 向量模型加载失败
**解决**: 
- 使用更小的模型
- 增加系统内存
- 使用简化版

#### 4. 网络连接问题
**问题**: 模型下载失败
**解决**: 
- 检查网络连接
- 使用代理
- 使用离线模型

### 性能优化建议

#### 1. 阈值调优
```python
# 测试不同阈值
thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]
for threshold in thresholds:
    evaluator.similarity_threshold = threshold
    # 运行评估
```

#### 2. 检索方法选择
- **向量检索**: 适合语义相似查询
- **BM25检索**: 适合关键词匹配
- **RRF融合**: 平衡两种方法的优势

#### 3. 知识库优化
- 确保内容与查询相关
- 适当的文档分块大小
- 高质量的文档预处理

## 📞 技术支持

### 获取帮助
1. 查看错误日志和输出信息
2. 检查环境配置和依赖安装
3. 参考改进总结文档
4. 使用简化版作为备选方案

### 贡献改进
欢迎提出改进建议和bug报告，帮助完善RAG系统评估功能。

---

**最后更新**: 2024年
**版本**: v2.0 改进版

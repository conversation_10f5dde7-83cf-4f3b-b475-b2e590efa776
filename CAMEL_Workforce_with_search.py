from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType, TaskType
from camel.societies.workforce import Workforce
from camel.toolkits import SearchToolkit
from camel.messages import BaseMessage
from camel.tasks import Task
import os
import time
import asyncio
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 创建模型实例
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

print("🔧 正在创建带搜索功能的 Workforce...")

#--------------------------------------------------
# 自定义搜索工具类，添加速率限制控制
#--------------------------------------------------

class RateLimitedSearchToolkit:
    """带速率限制的搜索工具包"""
    
    def __init__(self, delay_seconds=3):
        self.search_toolkit = SearchToolkit()
        self.delay_seconds = delay_seconds
        self.last_search_time = 0
    
    def safe_search_duckduckgo(self, query: str, max_results: int = 5):
        """安全的搜索方法，包含速率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_search_time
        
        if time_since_last < self.delay_seconds:
            sleep_time = self.delay_seconds - time_since_last
            print(f"⏳ 等待 {sleep_time:.1f} 秒以避免速率限制...")
            time.sleep(sleep_time)
        
        try:
            result = self.search_toolkit.search_duckduckgo(query, max_results)
            self.last_search_time = time.time()
            print(f"✅ 搜索成功: {query[:50]}...")
            return result
        except Exception as e:
            print(f"⚠️ 搜索失败: {str(e)}")
            return f"搜索暂时不可用，请使用已有知识回答。错误: {str(e)}"

# 创建速率限制搜索工具实例
safe_search = RateLimitedSearchToolkit(delay_seconds=5)

#--------------------------------------------------
# 定义工作节点（带安全搜索功能）
#--------------------------------------------------

# 1. 研究员 Agent：使用安全搜索工具
researcher_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="东京旅游信息研究员",
        content="""你是一个专业的东京旅游信息研究员。你的职责是：
        1. 使用搜索工具获取最新的东京旅游信息
        2. 如果搜索不可用，使用你的专业知识提供信息
        3. 重点关注：热门景点、特色美食、交通方式、购物场所
        4. 特别注意现代与传统结合的地方
        5. 提供实用、准确的旅游建议
        
        搜索策略：
        - 使用简洁的关键词避免复杂查询
        - 如果遇到速率限制，依靠专业知识回答
        - 优先提供2025年最新信息"""
    ),
    model=model,
    tools=[safe_search.safe_search_duckduckgo]  # 使用安全搜索工具
)

# 2. 规划师 Agent
planner_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="高级旅游规划师",
        content="""你是一位经验丰富的东京旅游规划师。你的职责是：
        1. 根据研究员提供的信息制定详细行程
        2. 考虑交通便利性和时间安排
        3. 平衡现代与传统文化体验
        4. 安排合理的购物和美食时间
        5. 确保行程适合首次访问东京的游客
        
        规划原则：
        - 每天不超过3-4个主要景点
        - 合理安排休息时间
        - 考虑地理位置的邻近性
        - 包含详细的交通指南"""
    ),
    model=model
)

# 3. 评审员 Agent
reviewer_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="资深旅行评论员",
        content="""你是一位资深的旅行评论员。你的职责是：
        1. 从实际旅行者角度评估行程
        2. 识别潜在问题和改进空间
        3. 提供具体的优化建议
        4. 确保行程的实用性和趣味性
        
        评估维度：
        - 时间安排是否合理
        - 交通是否便利
        - 费用预算是否适中
        - 文化体验是否丰富
        - 是否适合目标游客群体"""
    ),
    model=model
)

print("✅ 所有工作节点已定义（包含安全搜索功能）")

#--------------------------------------------------
# 创建 Workforce 实例
#--------------------------------------------------

# 协调器 Agent
coordinator_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="工作组协调员",
        content="你是东京旅游规划工作组的协调员。你需要合理分配任务，协调各专家的工作，确保最终产出高质量的旅游行程规划。"
    ),
    model=model
)

# 任务代理 Agent
from camel.toolkits import TaskPlanningToolkit
task_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="任务规划专家",
        content="你是任务规划专家，负责将复杂的旅游规划任务分解为可执行的子任务，并合理安排执行顺序。"
    ),
    model=model,
    tools=TaskPlanningToolkit().get_tools()
)

# 创建 Workforce
travel_workforce = Workforce(
    description="东京旅游行程规划与评估工作组（带搜索功能）",
    coordinator_agent=coordinator_agent,
    task_agent=task_agent
)

# 添加工作节点
travel_workforce.add_single_agent_worker(
    "负责搜索和提供东京最新的旅游信息，包括景点、美食、交通等",
    worker=researcher_agent
).add_single_agent_worker(
    "负责制定详细的东京3日旅游行程规划",
    worker=planner_agent
).add_single_agent_worker(
    "负责评估行程质量并提出优化建议",
    worker=reviewer_agent
)

print("✅ Workforce 实例已创建（包含搜索功能）")

#--------------------------------------------------
# 创建和处理任务
#--------------------------------------------------

tokyo_task = Task(
    content="请为一位首次访问东京的游客规划一份详细的3日旅游行程。需要包含热门景点、特色美食体验，并考虑交通便利性。请获取2025年最新的旅游信息。",
    additional_info={
        "游客偏好": "希望体验东京的现代与传统结合，对购物和美食有兴趣",
        "当前日期": "2025年7月17日",
        "旅行天数": "3天",
        "游客类型": "首次访问东京的游客",
        "特殊要求": "获取最新信息，避免过于紧凑的行程",
        "预算考虑": "中等预算，注重性价比"
    },
    id="tokyo_trip_plan_with_search"
)

print(f"\n🚀 开始处理带搜索功能的任务...")
print(f"任务: {tokyo_task.content[:80]}...")

try:
    # 处理任务
    print("\n⏳ 正在协调多智能体工作...")
    processed_task = travel_workforce.process_task(tokyo_task)
    
    print("\n" + "="*80)
    print("📋 任务处理完成 - 带搜索功能版本")
    print("="*80)
    print(f"任务ID: {processed_task.id}")
    print(f"\n🎯 最终结果:")
    print("-" * 60)
    print(processed_task.result)
    print("-" * 60)
    print("\n🎉 带搜索功能的 Workforce 演示成功！")
    print("\n💡 提示：如果搜索遇到速率限制，系统会自动使用内置知识库")
    
except Exception as e:
    print(f"\n❌ 任务处理出现错误: {str(e)}")
    print("可能的原因：")
    print("1. 网络连接问题")
    print("2. 搜索服务速率限制")
    print("3. 模型配置问题")
    print("\n💡 建议：可以尝试运行不带搜索功能的版本")
"""
RAG检索评估演示脚本

该脚本专门用于演示RAG系统的评估功能，包括：
1. 准确率(Precision)评估
2. 召回率(Recall)评估  
3. F1值计算
4. 相似度评估
5. 多种检索方法的性能对比

评估指标说明：
- 准确率：衡量检索结果的相关性 (检索到的相关文档数 / 检索到的总文档数)
- 召回率：衡量检索结果的覆盖范围 (检索到的相关文档数 / 所有相关文档数)
- F1值：综合考虑准确率和召回率的调和平均数
- 相似度：使用TF-IDF和余弦相似度计算预期答案与检索结果的相似度
"""

import os
import numpy as np
from typing import List, Dict, Any

# 导入CAMEL框架相关模块
from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever, BM25Retriever
from camel.storages.vectordb_storages import QdrantStorage

# 导入评估相关模块
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class SimpleRAGEvaluator:
    """简化的RAG评估器"""
    
    def __init__(self, similarity_threshold: float = 0.5):
        self.similarity_threshold = similarity_threshold
    
    def compute_similarity(self, expected: str, retrieved: str) -> float:
        """计算两个文本的相似度"""
        try:
            vectorizer = TfidfVectorizer()
            tfidf = vectorizer.fit_transform([expected, retrieved])
            similarity_matrix = cosine_similarity(tfidf, tfidf)
            return similarity_matrix[0, 1]
        except:
            return 0.0
    
    def calculate_precision(self, retrieved: List[str], relevant: List[str], threshold: float = None) -> float:
        """计算精确率"""
        if not retrieved:
            return 0.0
        
        threshold = threshold or self.similarity_threshold
        correct = 0
        
        for r in retrieved:
            for rel in relevant:
                if self.compute_similarity(rel, r) >= threshold:
                    correct += 1
                    break
        
        return correct / len(retrieved)
    
    def calculate_recall(self, retrieved: List[str], relevant: List[str], threshold: float = None) -> float:
        """计算召回率"""
        if not relevant:
            return 0.0
        
        threshold = threshold or self.similarity_threshold
        correct = 0
        
        for rel in relevant:
            for r in retrieved:
                if self.compute_similarity(rel, r) >= threshold:
                    correct += 1
                    break
        
        return correct / len(relevant)
    
    def calculate_f1(self, precision: float, recall: float) -> float:
        """计算F1值"""
        if precision + recall == 0:
            return 0.0
        return 2 * (precision * recall) / (precision + recall)
    
    def evaluate_retrieval(self, query: str, expected_answers: List[str], 
                          retrieved_texts: List[str], threshold: float = None) -> Dict[str, Any]:
        """评估单个查询的检索质量"""
        threshold = threshold or self.similarity_threshold
        
        precision = self.calculate_precision(retrieved_texts, expected_answers, threshold)
        recall = self.calculate_recall(retrieved_texts, expected_answers, threshold)
        f1 = self.calculate_f1(precision, recall)
        
        # 计算平均相似度
        similarities = []
        for expected in expected_answers:
            for retrieved in retrieved_texts:
                similarities.append(self.compute_similarity(expected, retrieved))
        
        avg_similarity = np.mean(similarities) if similarities else 0.0
        
        return {
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "avg_similarity": avg_similarity,
            "retrieved_texts": retrieved_texts
        }

def create_sample_document():
    """创建示例文档"""
    sample_content = """# CAMEL AI 介绍

CAMEL AI 是一个开源的、社区驱动的AI框架，旨在简化AI应用的开发和部署。该框架提供了多种功能模块，包括数据加载、特征工程、模型训练和部署等。

## 主要特点

### 模块化设计
用户可以根据需求选择性地加载不同的功能模块。

### 易用性
提供了简单易用的API接口，降低了开发门槛。

### 扩展性
支持多种模型和后端服务，方便用户根据需求进行扩展。

## 常见问题

### 如何开始使用CAMEL AI？

首先安装框架：pip install camel-ai

引入必要的模块：from camel import *

参考官方文档：CAMEL AI官方文档

### CAMEL AI的核心功能

CAMEL AI支持多智能体协作、角色扮演对话、任务分解和执行等核心功能。

### 角色扮演实现

角色扮演功能通过定义角色特征、设置对话上下文、实现交互逻辑来实现。
"""
    
    os.makedirs('local_data', exist_ok=True)
    sample_path = 'local_data/camel_ai_evaluation_sample.md'
    
    with open(sample_path, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    return sample_path

def setup_retrievers(document_path: str):
    """设置检索器"""
    print("设置检索器...")
    
    # 初始化嵌入模型
    embedding_model = SentenceTransformerEncoder(model_name='intfloat/e5-large-v2')
    
    # 初始化向量存储
    vector_storage = QdrantStorage(
        vector_dim=embedding_model.get_output_dim(),
        collection="evaluation_collection",
        path="storage_evaluation",
        collection_name="评估测试"
    )
    
    # 初始化检索器
    vr = VectorRetriever(embedding_model=embedding_model, storage=vector_storage)
    bm25r = BM25Retriever()
    
    # 处理文档
    print("处理文档并建立索引...")
    vr.process(content=document_path)
    bm25r.process(content_input_path=document_path)
    
    return vr, bm25r

def rrf_fusion(vector_results: List[Dict], text_results: List[Dict], k: int = 10, m: int = 60):
    """RRF算法融合"""
    doc_scores = {}
    
    for rank, result in enumerate(vector_results):
        text = result['text']
        doc_scores[text] = doc_scores.get(text, 0) + 1 / (rank + m)
    
    for rank, result in enumerate(text_results):
        text = result['text']
        doc_scores[text] = doc_scores.get(text, 0) + 1 / (rank + m)
    
    sorted_results = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:k]
    return [{"text": text, "rrf_score": score} for text, score in sorted_results]

def main():
    """主函数"""
    print("=" * 60)
    print("RAG检索评估演示")
    print("=" * 60)
    
    # 创建示例文档
    print("创建示例文档...")
    document_path = create_sample_document()
    print(f"示例文档已创建: {document_path}")
    
    # 设置检索器
    vr, bm25r = setup_retrievers(document_path)
    
    # 定义测试用例
    test_queries = [
        {
            "query": "什么是CAMEL AI？",
            "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"]
        },
        {
            "query": "如何开始使用CAMEL AI？",
            "expected_answers": ["首先安装框架：pip install camel-ai，然后引入必要的模块。"]
        },
        {
            "query": "CAMEL AI 的主要特点是什么？",
            "expected_answers": ["模块化设计、易用性和扩展性。"]
        }
    ]
    
    # 初始化评估器
    evaluator = SimpleRAGEvaluator(similarity_threshold=0.5)
    
    # 定义检索方法
    retrieval_methods = {
        "向量检索": lambda q: vr.query(query=q, top_k=3),
        "BM25检索": lambda q: bm25r.query(query=q, top_k=3),
        "RRF融合": lambda q: rrf_fusion(
            vr.query(query=q, top_k=5), 
            bm25r.query(query=q, top_k=5), 
            k=3
        )
    }
    
    # 执行评估
    all_results = {}
    
    for method_name, retrieval_func in retrieval_methods.items():
        print(f"\n{'='*20} 评估 {method_name} {'='*20}")
        
        method_results = []
        
        for test_case in test_queries:
            query = test_case["query"]
            expected_answers = test_case["expected_answers"]
            
            print(f"\n查询: {query}")
            print(f"预期答案: {expected_answers}")
            
            # 执行检索
            results = retrieval_func(query)
            retrieved_texts = [result["text"] for result in results]
            
            print(f"检索结果:")
            for i, text in enumerate(retrieved_texts):
                print(f"  {i+1}. {text[:100]}...")
            
            # 评估
            evaluation = evaluator.evaluate_retrieval(query, expected_answers, retrieved_texts)
            method_results.append(evaluation)
            
            print(f"精确率: {evaluation['precision']:.4f}")
            print(f"召回率: {evaluation['recall']:.4f}")
            print(f"F1分数: {evaluation['f1']:.4f}")
            print(f"平均相似度: {evaluation['avg_similarity']:.4f}")
            print("-" * 60)
        
        # 计算平均指标
        avg_precision = np.mean([r["precision"] for r in method_results])
        avg_recall = np.mean([r["recall"] for r in method_results])
        avg_f1 = np.mean([r["f1"] for r in method_results])
        avg_similarity = np.mean([r["avg_similarity"] for r in method_results])
        
        all_results[method_name] = {
            "avg_precision": avg_precision,
            "avg_recall": avg_recall,
            "avg_f1": avg_f1,
            "avg_similarity": avg_similarity
        }
        
        print(f"\n{method_name} 平均指标:")
        print(f"平均精确率: {avg_precision:.4f}")
        print(f"平均召回率: {avg_recall:.4f}")
        print(f"平均F1分数: {avg_f1:.4f}")
        print(f"平均相似度: {avg_similarity:.4f}")
    
    # 生成对比报告
    print("\n" + "=" * 60)
    print("检索方法性能对比")
    print("=" * 60)
    print(f"{'方法':<15} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'相似度':<10}")
    print("-" * 60)
    
    for method_name, metrics in all_results.items():
        print(f"{method_name:<15} {metrics['avg_precision']:<10.4f} {metrics['avg_recall']:<10.4f} "
              f"{metrics['avg_f1']:<10.4f} {metrics['avg_similarity']:<10.4f}")
    
    # 找出最佳方法
    best_method = max(all_results.items(), key=lambda x: x[1]['avg_f1'])
    print(f"\n最佳检索方法: {best_method[0]} (F1分数: {best_method[1]['avg_f1']:.4f})")
    
    print("\n" + "=" * 60)
    print("评估完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()

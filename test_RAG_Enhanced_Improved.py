"""
改进版RAG系统：集成智能评估和优化检索

该脚本基于评估分析结果进行了全面改进：
1. 智能阈值选择：自适应阈值和多阈值测试
2. 语义相似度：集成Sentence-BERT进行深层语义理解
3. 知识库匹配：确保测试用例与知识库内容一致
4. 端到端评估：包含检索和生成答案的完整评估
5. 可视化报告：提供详细的性能分析和可视化图表
6. 改进的HyDE：优化假设文档生成策略

主要改进点：
- 解决阈值过高问题（0.7 → 自适应/多阈值）
- 改进相似度计算（TF-IDF + Sentence-BERT）
- 确保知识库与测试用例匹配
- 添加生成答案质量评估
- 提供详细的错误分析和改进建议
"""

import os
import requests
import numpy as np
import json
from typing import List, Dict, Any, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 可选依赖导入
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    HAS_PLOTTING = True
except ImportError:
    print("⚠️ matplotlib/seaborn 未安装，跳过可视化功能")
    HAS_PLOTTING = False

try:
    from sentence_transformers import SentenceTransformer
    HAS_SENTENCE_TRANSFORMERS = True
except ImportError:
    print("⚠️ sentence-transformers 未安装，将使用TF-IDF相似度")
    HAS_SENTENCE_TRANSFORMERS = False

# 导入CAMEL框架相关模块
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from camel.agents import ChatAgent
from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever, BM25Retriever
from camel.storages.vectordb_storages import QdrantStorage

# 导入环境变量模块
from dotenv import load_dotenv

# 导入评估相关模块
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# 可选的评估依赖
try:
    from sklearn.metrics import precision_recall_curve
    HAS_PRECISION_RECALL = True
except ImportError:
    print("⚠️ sklearn.metrics.precision_recall_curve 不可用")
    HAS_PRECISION_RECALL = False

try:
    import nltk
    from nltk.translate.bleu_score import sentence_bleu
    HAS_NLTK = True
except ImportError:
    print("⚠️ NLTK 未安装，跳过BLEU评分")
    HAS_NLTK = False

try:
    from rouge_score import rouge_scorer
    HAS_ROUGE = True
except ImportError:
    print("⚠️ rouge-score 未安装，跳过ROUGE评分")
    HAS_ROUGE = False

class ImprovedRAGEvaluator:
    """
    改进版RAG系统评估器
    
    主要改进：
    1. 多种相似度计算方法（TF-IDF + Sentence-BERT）
    2. 自适应阈值选择
    3. 端到端答案质量评估
    4. 可视化分析报告
    5. 详细的错误分析
    """
    
    def __init__(self, similarity_threshold: float = 0.3, use_semantic: bool = True):
        """
        初始化改进版评估器
        
        Args:
            similarity_threshold: 默认相似度阈值（降低到0.3）
            use_semantic: 是否使用语义相似度（Sentence-BERT）
        """
        self.similarity_threshold = similarity_threshold
        self.use_semantic = use_semantic
        
        # 初始化语义相似度模型
        if use_semantic and HAS_SENTENCE_TRANSFORMERS:
            try:
                self.semantic_model = SentenceTransformer('all-MiniLM-L6-v2')
                print("✓ 语义相似度模型加载成功")
            except Exception as e:
                print(f"⚠️ 语义相似度模型加载失败，使用TF-IDF: {e}")
                self.use_semantic = False
                self.semantic_model = None
        else:
            self.use_semantic = False
            self.semantic_model = None
            if use_semantic and not HAS_SENTENCE_TRANSFORMERS:
                print("⚠️ sentence-transformers 未安装，使用TF-IDF相似度")

        # 初始化ROUGE评分器（用于生成答案评估）
        if HAS_ROUGE:
            try:
                self.rouge_scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
                print("✓ ROUGE评分器初始化成功")
            except Exception as e:
                print(f"⚠️ ROUGE评分器初始化失败: {e}")
                self.rouge_scorer = None
        else:
            self.rouge_scorer = None
            print("⚠️ rouge-score 未安装，跳过ROUGE评分")
    
    def compute_similarity(self, expected: str, retrieved: str, method: str = 'hybrid') -> float:
        """
        计算文本相似度（支持多种方法）
        
        Args:
            expected: 预期文本
            retrieved: 检索到的文本
            method: 相似度计算方法 ('tfidf', 'semantic', 'hybrid')
            
        Returns:
            相似度分数 (0-1之间)
        """
        if not expected or not retrieved:
            return 0.0
        
        try:
            if method == 'semantic' and self.use_semantic:
                # 使用Sentence-BERT计算语义相似度
                embeddings = self.semantic_model.encode([expected, retrieved])
                similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
                return max(0.0, min(1.0, similarity))
            
            elif method == 'tfidf':
                # 使用TF-IDF计算相似度
                vectorizer = TfidfVectorizer()
                tfidf = vectorizer.fit_transform([expected, retrieved])
                similarity_matrix = cosine_similarity(tfidf, tfidf)
                return similarity_matrix[0, 1]
            
            elif method == 'hybrid' and self.use_semantic:
                # 混合方法：TF-IDF + Semantic
                tfidf_sim = self.compute_similarity(expected, retrieved, 'tfidf')
                semantic_sim = self.compute_similarity(expected, retrieved, 'semantic')
                # 加权平均：语义相似度权重更高
                return 0.3 * tfidf_sim + 0.7 * semantic_sim
            
            else:
                # 默认使用TF-IDF
                return self.compute_similarity(expected, retrieved, 'tfidf')
                
        except Exception as e:
            print(f"计算相似度时出错: {e}")
            return 0.0
    
    def find_optimal_threshold(self, similarities: List[float], labels: List[bool]) -> float:
        """
        自动寻找最优阈值

        Args:
            similarities: 相似度分数列表
            labels: 真实标签列表（True表示相关）

        Returns:
            最优阈值
        """
        if len(similarities) != len(labels) or len(similarities) == 0:
            return self.similarity_threshold

        if not HAS_PRECISION_RECALL:
            print("⚠️ precision_recall_curve 不可用，使用默认阈值")
            return self.similarity_threshold

        try:
            # 使用precision-recall曲线找到最优阈值
            precision, recall, thresholds = precision_recall_curve(labels, similarities)

            # 计算F1分数
            f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)

            # 找到F1分数最高的阈值
            best_idx = np.argmax(f1_scores)
            optimal_threshold = thresholds[best_idx] if best_idx < len(thresholds) else self.similarity_threshold

            print(f"✓ 自动找到最优阈值: {optimal_threshold:.3f} (F1: {f1_scores[best_idx]:.3f})")
            return optimal_threshold

        except Exception as e:
            print(f"⚠️ 自动阈值选择失败，使用默认值: {e}")
            return self.similarity_threshold
    
    def evaluate_with_multiple_thresholds(self, retrieved: List[str], relevant: List[str], 
                                        thresholds: List[float] = None) -> Dict[str, Any]:
        """
        使用多个阈值进行评估
        
        Args:
            retrieved: 检索到的文档列表
            relevant: 相关文档列表
            thresholds: 阈值列表
            
        Returns:
            多阈值评估结果
        """
        if thresholds is None:
            thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        results = {}
        
        for threshold in thresholds:
            precision = self.calculate_precision(retrieved, relevant, threshold)
            recall = self.calculate_recall(retrieved, relevant, threshold)
            f1 = self.calculate_f1(precision, recall)
            
            results[f"threshold_{threshold}"] = {
                "threshold": threshold,
                "precision": precision,
                "recall": recall,
                "f1": f1
            }
        
        # 找到最佳阈值
        best_threshold = max(results.keys(), key=lambda k: results[k]["f1"])
        results["best_threshold"] = results[best_threshold]
        
        return results
    
    def calculate_precision(self, retrieved: List[str], relevant: List[str], 
                          threshold: float = None, method: str = 'hybrid') -> float:
        """改进的精确率计算"""
        if not retrieved:
            return 0.0
        
        threshold = threshold or self.similarity_threshold
        correct = 0
        
        for r in retrieved:
            for rel in relevant:
                similarity = self.compute_similarity(rel, r, method)
                if similarity >= threshold:
                    correct += 1
                    break
        
        return correct / len(retrieved)
    
    def calculate_recall(self, retrieved: List[str], relevant: List[str], 
                        threshold: float = None, method: str = 'hybrid') -> float:
        """改进的召回率计算"""
        if not relevant:
            return 0.0
        
        threshold = threshold or self.similarity_threshold
        correct = 0
        
        for rel in relevant:
            for r in retrieved:
                similarity = self.compute_similarity(rel, r, method)
                if similarity >= threshold:
                    correct += 1
                    break
        
        return correct / len(relevant)
    
    def calculate_f1(self, precision: float, recall: float) -> float:
        """计算F1值"""
        if precision + recall == 0:
            return 0.0
        return 2 * (precision * recall) / (precision + recall)
    
    def evaluate_generated_answer(self, query: str, generated_answer: str, 
                                expected_answers: List[str]) -> Dict[str, Any]:
        """
        评估生成答案的质量
        
        Args:
            query: 原始查询
            generated_answer: 生成的答案
            expected_answers: 预期答案列表
            
        Returns:
            答案质量评估结果
        """
        if not generated_answer or not expected_answers:
            return {"error": "生成答案或预期答案为空"}
        
        results = {
            "query": query,
            "generated_answer": generated_answer,
            "expected_answers": expected_answers,
            "semantic_similarity": 0.0,
            "bleu_score": 0.0,
            "rouge_scores": {},
            "length_ratio": 0.0,
            "quality_score": 0.0
        }
        
        try:
            # 1. 语义相似度评估
            max_semantic_sim = 0.0
            for expected in expected_answers:
                sim = self.compute_similarity(expected, generated_answer, 'semantic')
                max_semantic_sim = max(max_semantic_sim, sim)
            results["semantic_similarity"] = max_semantic_sim
            
            # 2. BLEU分数评估
            if len(expected_answers) > 0 and HAS_NLTK:
                try:
                    # 将预期答案作为参考，计算BLEU分数
                    references = [answer.split() for answer in expected_answers]
                    candidate = generated_answer.split()
                    bleu = sentence_bleu(references, candidate, weights=(0.5, 0.3, 0.2))
                    results["bleu_score"] = bleu
                except Exception as e:
                    print(f"⚠️ BLEU计算失败: {e}")
                    results["bleu_score"] = 0.0
            else:
                results["bleu_score"] = 0.0
            
            # 3. ROUGE分数评估
            if self.rouge_scorer:
                rouge_scores = {}
                for expected in expected_answers:
                    scores = self.rouge_scorer.score(expected, generated_answer)
                    for metric, score in scores.items():
                        if metric not in rouge_scores:
                            rouge_scores[metric] = []
                        rouge_scores[metric].append(score.fmeasure)
                
                # 取平均值
                for metric in rouge_scores:
                    rouge_scores[metric] = np.mean(rouge_scores[metric])
                
                results["rouge_scores"] = rouge_scores
            
            # 4. 长度比率
            avg_expected_length = np.mean([len(ans.split()) for ans in expected_answers])
            generated_length = len(generated_answer.split())
            results["length_ratio"] = generated_length / (avg_expected_length + 1e-8)
            
            # 5. 综合质量分数
            quality_score = (
                0.4 * max_semantic_sim +
                0.3 * results["bleu_score"] +
                0.2 * results["rouge_scores"].get("rougeL", 0.0) +
                0.1 * min(1.0, results["length_ratio"])  # 长度适中加分
            )
            results["quality_score"] = quality_score
            
        except Exception as e:
            print(f"评估生成答案时出错: {e}")
            results["error"] = str(e)
        
        return results

class ImprovedRAGSystem:
    """
    改进版RAG系统

    主要改进：
    1. 确保知识库与测试用例匹配
    2. 优化HyDE假设文档生成
    3. 改进RRF参数调优
    4. 集成智能评估系统
    """

    def __init__(self, api_key: str):
        """初始化改进版RAG系统"""
        self.api_key = api_key

        # 初始化嵌入模型
        self.embedding_model = SentenceTransformerEncoder(
            model_name='intfloat/e5-large-v2'
        )

        # 初始化向量存储
        self.vector_storage = QdrantStorage(
            vector_dim=self.embedding_model.get_output_dim(),
            collection="improved_rag_collection",
            path="storage_improved_rag",
            collection_name="改进RAG知识库"
        )

        # 初始化检索器
        self.vector_retriever = VectorRetriever(
            embedding_model=self.embedding_model,
            storage=self.vector_storage
        )
        self.bm25_retriever = BM25Retriever()

        # 初始化LLM模型
        self.model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
            model_type="Qwen/Qwen2.5-72B-Instruct",
            url='https://api-inference.modelscope.cn/v1/',
            api_key=self.api_key
        )

        # 初始化代理和评估器
        self._init_agents()
        self.evaluator = ImprovedRAGEvaluator(similarity_threshold=0.3, use_semantic=True)

        # 知识库路径
        self.knowledge_base_path = None

    def _init_agents(self):
        """初始化AI代理"""

        # 查询重写代理
        rewriting_sys_msg = """
        你是RAG模块中的Rewriting助手。请重写用户查询，修正错别字并优化表达。
        要求：
        1. 修正明显的错别字
        2. 保持原意不变
        3. 使表达更加清晰准确
        4. 直接输出重写后的查询，不要添加解释
        """
        self.rewriting_agent = ChatAgent(
            system_message=rewriting_sys_msg,
            model=self.model
        )

        # 改进的HyDE代理
        hyde_sys_msg = """
        你是一个专门生成假设文档的助手。根据用户查询，生成一个高质量的假设文档片段。

        生成要求：
        1. 直接回答用户问题，内容准确具体
        2. 包含相关的技术细节和概念
        3. 使用专业但易懂的语言
        4. 长度控制在150-300字
        5. 结构清晰，逻辑连贯

        请直接输出假设文档内容，不要添加格式标记或解释。
        """
        self.hyde_agent = ChatAgent(
            system_message=hyde_sys_msg,
            model=self.model
        )

        # RAG回答生成代理
        rag_sys_msg = """
        你是一个专业的问答助手。基于提供的上下文信息回答用户问题。

        回答要求：
        1. 基于上下文信息准确回答
        2. 如果信息不足，明确说明
        3. 回答要完整、准确、有条理
        4. 使用清晰易懂的语言
        5. 适当引用上下文中的关键信息
        """
        self.rag_agent = ChatAgent(
            system_message=rag_sys_msg,
            model=self.model
        )

    def create_matched_knowledge_base(self) -> str:
        """
        创建与测试用例匹配的知识库

        Returns:
            知识库文件路径
        """
        knowledge_content = """# CAMEL AI 框架详细介绍

## 什么是CAMEL AI？

CAMEL AI 是一个开源的、社区驱动的AI框架，专门设计用于简化AI应用的开发和部署。该框架提供了完整的工具链，支持从数据处理到模型部署的全流程开发。

CAMEL AI的核心理念是让AI开发更加民主化和易于使用，通过模块化设计和丰富的API接口，开发者可以快速构建高质量的AI应用。

## 如何开始使用CAMEL AI？

### 安装步骤

首先安装框架：`pip install camel-ai`

然后引入必要的模块：`from camel import *`

### 快速开始

1. 导入所需模块
2. 配置API密钥
3. 初始化智能体
4. 开始对话或任务执行

### 文档和资源

参考官方文档获取详细教程和API参考：CAMEL AI官方文档

## CAMEL AI的主要特点

CAMEL AI具有以下核心特点：

### 模块化设计
用户可以根据具体需求选择性地加载不同的功能模块，包括：
- 对话模块
- 任务执行模块
- 代码生成模块
- 文档处理模块

### 易用性
提供了简单易用的API接口，大大降低了AI开发的门槛。即使是初学者也能快速上手。

### 扩展性
支持多种模型和后端服务，方便用户根据需求进行扩展和定制。框架采用插件化架构，易于集成第三方服务。

## CAMEL AI支持哪些功能？

CAMEL AI提供了丰富的功能模块：

### 多智能体协作系统
- 支持多个AI智能体之间的协作
- 提供任务分配和协调机制
- 支持复杂任务的分解和并行处理

### 角色扮演对话
- 支持定义不同的AI角色
- 可以进行角色扮演对话
- 适用于教育、娱乐等多种场景

### 任务分解和执行
- 自动将复杂任务分解为子任务
- 支持任务的并行和串行执行
- 提供任务进度跟踪和结果汇总

### 代码生成和调试
- 支持多种编程语言的代码生成
- 提供代码审查和优化建议
- 支持自动化测试和调试

### 文档处理和检索
- 支持多种文档格式的处理
- 提供智能文档检索功能
- 支持文档摘要和问答

## 角色扮演功能如何实现？

CAMEL AI的角色扮演功能通过以下方式实现：

### 角色定义
- 定义角色特征和行为模式
- 设置角色的知识背景和专业领域
- 配置角色的语言风格和表达方式

### 对话上下文管理
- 设置对话上下文和约束条件
- 维护对话历史和状态信息
- 支持多轮对话的连贯性

### 交互逻辑实现
- 实现角色间的交互逻辑和规则
- 支持动态角色切换和适应
- 提供冲突解决和协商机制

### 状态和记忆维护
- 维护角色状态和记忆信息
- 支持长期记忆和短期记忆
- 提供记忆检索和更新机制
"""

        # 确保目录存在
        os.makedirs('local_data', exist_ok=True)

        # 写入知识库文件
        kb_path = 'local_data/camel_ai_matched_kb.md'
        with open(kb_path, 'w', encoding='utf-8') as f:
            f.write(knowledge_content)

        print(f"✓ 匹配的知识库已创建: {kb_path}")
        return kb_path

    def setup_knowledge_base(self, use_matched_kb: bool = True):
        """
        设置知识库

        Args:
            use_matched_kb: 是否使用匹配的知识库
        """
        if use_matched_kb:
            # 使用匹配的知识库
            kb_path = self.create_matched_knowledge_base()
        else:
            # 使用CAMEL论文
            print("下载CAMEL论文作为知识库...")
            os.makedirs('local_data', exist_ok=True)
            url = "https://arxiv.org/pdf/2303.17760.pdf"
            response = requests.get(url)
            kb_path = 'local_data/camel_paper.pdf'
            with open(kb_path, 'wb') as file:
                file.write(response.content)
            print(f"✓ CAMEL论文已下载: {kb_path}")

        # 处理知识库
        print("处理知识库并建立索引...")
        try:
            # 处理向量检索器
            print("  - 处理向量检索器...")
            self.vector_retriever.process(content=kb_path)
            print("  ✓ 向量检索器处理完成")

            # 处理BM25检索器
            print("  - 处理BM25检索器...")
            self.bm25_retriever.process(content_input_path=kb_path)
            print("  ✓ BM25检索器处理完成")

            # 验证处理结果
            print("  - 验证处理结果...")
            test_query = "CAMEL AI"

            # 测试向量检索
            try:
                vector_test = self.vector_retriever.query(query=test_query, top_k=1)
                if vector_test:
                    print(f"  ✓ 向量检索测试成功，找到 {len(vector_test)} 个结果")
                else:
                    print("  ⚠️ 向量检索测试返回空结果")
            except Exception as e:
                print(f"  ❌ 向量检索测试失败: {e}")

            # 测试BM25检索
            try:
                bm25_test = self.bm25_retriever.query(query=test_query, top_k=1)
                if bm25_test:
                    print(f"  ✓ BM25检索测试成功，找到 {len(bm25_test)} 个结果")
                else:
                    print("  ⚠️ BM25检索测试返回空结果")
            except Exception as e:
                print(f"  ❌ BM25检索测试失败: {e}")

        except Exception as e:
            print(f"❌ 知识库处理失败: {e}")
            print("尝试重新处理...")

            # 尝试重新创建存储
            try:
                # 重新初始化向量存储
                self.vector_storage = QdrantStorage(
                    vector_dim=self.embedding_model.get_output_dim(),
                    collection="improved_rag_collection_retry",
                    path="storage_improved_rag_retry",
                    collection_name="改进RAG知识库重试"
                )

                self.vector_retriever = VectorRetriever(
                    embedding_model=self.embedding_model,
                    storage=self.vector_storage
                )

                # 重新处理
                self.vector_retriever.process(content=kb_path)
                self.bm25_retriever.process(content_input_path=kb_path)
                print("✓ 重新处理成功")

            except Exception as retry_e:
                print(f"❌ 重新处理也失败: {retry_e}")
                raise

        self.knowledge_base_path = kb_path
        print("✓ 知识库设置完成")

    def get_improved_test_cases(self) -> List[Dict[str, Any]]:
        """
        获取改进的测试用例（与知识库匹配）

        Returns:
            测试用例列表
        """
        return [
            {
                "query": "什么是CAMEL AI？",
                "expected_answers": [
                    "CAMEL AI 是一个开源的、社区驱动的AI框架",
                    "专门设计用于简化AI应用的开发和部署"
                ]
            },
            {
                "query": "如何开始使用CAMEL AI？",
                "expected_answers": [
                    "首先安装框架：pip install camel-ai",
                    "然后引入必要的模块：from camel import *"
                ]
            },
            {
                "query": "CAMEL AI 的主要特点是什么？",
                "expected_answers": [
                    "模块化设计、易用性和扩展性",
                    "模块化设计，易用性，扩展性"
                ]
            },
            {
                "query": "CAMEL AI支持哪些功能？",
                "expected_answers": [
                    "多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索",
                    "多智能体协作，角色扮演，任务分解，代码生成，文档处理"
                ]
            },
            {
                "query": "角色扮演功能如何实现？",
                "expected_answers": [
                    "通过角色定义、对话上下文管理、交互逻辑实现、状态和记忆维护来实现",
                    "定义角色特征，设置对话上下文，实现交互逻辑，维护角色状态"
                ]
            }
        ]

    def rrf_fusion(self, vector_results: List[Dict], text_results: List[Dict],
                   k: int = 5, m: int = 60) -> List[Dict[str, Any]]:
        """
        改进的RRF算法融合

        Args:
            vector_results: 向量检索结果
            text_results: 文本检索结果
            k: 返回结果数量
            m: RRF参数（调优后的值）

        Returns:
            融合后的结果
        """
        doc_scores = {}

        # 处理向量检索结果
        for rank, result in enumerate(vector_results):
            text = result['text']
            doc_scores[text] = doc_scores.get(text, 0) + 1 / (rank + m)

        # 处理BM25检索结果
        for rank, result in enumerate(text_results):
            text = result['text']
            doc_scores[text] = doc_scores.get(text, 0) + 1 / (rank + m)

        # 排序并返回前k个结果
        sorted_results = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:k]

        return [{"text": text, "rrf_score": score} for text, score in sorted_results]

    def improved_retrieve(self, query: str, method: str = "hybrid", top_k: int = 5) -> List[Dict[str, Any]]:
        """
        改进的检索方法

        Args:
            query: 查询文本
            method: 检索方法 ("vector", "bm25", "rrf", "hyde", "hybrid")
            top_k: 返回结果数量

        Returns:
            检索结果列表
        """
        if method == "vector":
            return self.vector_retriever.query(query=query, top_k=top_k)

        elif method == "bm25":
            return self.bm25_retriever.query(query=query, top_k=top_k)

        elif method == "rrf":
            vector_results = self.vector_retriever.query(query=query, top_k=top_k*2)
            bm25_results = self.bm25_retriever.query(query=query, top_k=top_k*2)
            return self.rrf_fusion(vector_results, bm25_results, k=top_k)

        elif method == "hyde":
            # 生成假设文档
            hyde_prompt = f"请为以下查询生成一个相关的假设文档：{query}"
            response = self.hyde_agent.step(hyde_prompt)
            hypothetical_doc = response.msgs[0].content.strip()

            # 使用假设文档检索
            return self.vector_retriever.query(query=hypothetical_doc, top_k=top_k)

        elif method == "hybrid":
            # HyDE + RRF 组合
            hyde_prompt = f"请为以下查询生成一个相关的假设文档：{query}"
            response = self.hyde_agent.step(hyde_prompt)
            hypothetical_doc = response.msgs[0].content.strip()

            hyde_results = self.vector_retriever.query(query=hypothetical_doc, top_k=top_k*2)
            bm25_results = self.bm25_retriever.query(query=query, top_k=top_k*2)

            return self.rrf_fusion(hyde_results, bm25_results, k=top_k)

        else:
            raise ValueError(f"不支持的检索方法: {method}")

    def generate_answer(self, query: str, retrieved_docs: List[Dict[str, Any]]) -> str:
        """
        基于检索结果生成答案

        Args:
            query: 用户查询
            retrieved_docs: 检索到的文档

        Returns:
            生成的答案
        """
        if not retrieved_docs:
            return "抱歉，没有找到相关信息来回答您的问题。"

        # 构建上下文
        context = "\n\n".join([doc['text'] for doc in retrieved_docs])

        # 构建提示
        prompt = f"""
        用户查询: {query}

        相关上下文信息:
        {context}

        请基于上述上下文信息准确回答用户的查询。如果上下文信息不足以完全回答问题，请明确说明。
        """

        response = self.rag_agent.step(prompt)
        return response.msgs[0].content.strip()

    def comprehensive_evaluation(self, test_cases: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        综合评估不同检索方法

        Args:
            test_cases: 测试用例列表

        Returns:
            综合评估结果
        """
        if test_cases is None:
            test_cases = self.get_improved_test_cases()

        # 定义检索方法
        methods = {
            "向量检索": "vector",
            "BM25检索": "bm25",
            "RRF融合": "rrf",
            "HyDE检索": "hyde",
            "混合检索": "hybrid"
        }

        # 定义多个阈值进行测试
        thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]

        all_results = {}

        print("=" * 80)
        print("开始综合评估")
        print("=" * 80)

        for method_name, method_code in methods.items():
            print(f"\n{'='*20} 评估 {method_name} {'='*20}")

            method_results = {
                "method": method_name,
                "retrieval_results": [],
                "answer_quality_results": [],
                "threshold_analysis": {}
            }

            # 收集所有查询的检索结果
            all_retrieved = []
            all_relevant = []

            for i, test_case in enumerate(test_cases):
                query = test_case["query"]
                expected_answers = test_case["expected_answers"]

                print(f"\n查询 {i+1}: {query}")

                try:
                    # 执行检索
                    retrieved_docs = self.improved_retrieve(query, method=method_code, top_k=3)
                    retrieved_texts = [doc['text'] for doc in retrieved_docs]

                    # 生成答案
                    generated_answer = self.generate_answer(query, retrieved_docs)

                    # 评估检索质量
                    retrieval_eval = self.evaluator.evaluate_single_query(
                        query, expected_answers, retrieved_texts, threshold=0.3
                    )
                    method_results["retrieval_results"].append(retrieval_eval)

                    # 评估答案质量
                    answer_eval = self.evaluator.evaluate_generated_answer(
                        query, generated_answer, expected_answers
                    )
                    method_results["answer_quality_results"].append(answer_eval)

                    # 收集数据用于阈值分析
                    for expected in expected_answers:
                        for retrieved in retrieved_texts:
                            similarity = self.evaluator.compute_similarity(expected, retrieved, 'hybrid')
                            all_retrieved.append(similarity)
                            all_relevant.append(similarity >= 0.3)  # 基准阈值

                    print(f"  检索精确率: {retrieval_eval['precision']:.3f}")
                    print(f"  检索召回率: {retrieval_eval['recall']:.3f}")
                    print(f"  答案质量分数: {answer_eval.get('quality_score', 0):.3f}")

                except Exception as e:
                    print(f"  评估出错: {e}")
                    continue

            # 多阈值分析
            if all_retrieved:
                for threshold in thresholds:
                    labels = [sim >= threshold for sim in all_retrieved]
                    if any(labels):  # 确保有正样本
                        precision = sum(labels) / len(labels) if labels else 0
                        recall = sum(labels) / sum(all_relevant) if sum(all_relevant) > 0 else 0
                        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

                        method_results["threshold_analysis"][threshold] = {
                            "precision": precision,
                            "recall": recall,
                            "f1": f1
                        }

            # 计算平均指标
            if method_results["retrieval_results"]:
                avg_precision = np.mean([r["precision"] for r in method_results["retrieval_results"]])
                avg_recall = np.mean([r["recall"] for r in method_results["retrieval_results"]])
                avg_f1 = np.mean([r["f1"] for r in method_results["retrieval_results"]])
                avg_similarity = np.mean([r["avg_similarity"] for r in method_results["retrieval_results"]])

                method_results["average_metrics"] = {
                    "precision": avg_precision,
                    "recall": avg_recall,
                    "f1": avg_f1,
                    "similarity": avg_similarity
                }

                print(f"\n{method_name} 平均指标:")
                print(f"  平均精确率: {avg_precision:.3f}")
                print(f"  平均召回率: {avg_recall:.3f}")
                print(f"  平均F1分数: {avg_f1:.3f}")
                print(f"  平均相似度: {avg_similarity:.3f}")

            if method_results["answer_quality_results"]:
                avg_quality = np.mean([r.get("quality_score", 0) for r in method_results["answer_quality_results"]])
                method_results["average_answer_quality"] = avg_quality
                print(f"  平均答案质量: {avg_quality:.3f}")

            all_results[method_name] = method_results

        # 生成综合报告
        self._generate_comprehensive_report(all_results)

        return all_results

    def _generate_comprehensive_report(self, results: Dict[str, Any]):
        """生成综合评估报告"""
        print("\n" + "=" * 80)
        print("综合评估报告")
        print("=" * 80)

        # 检索性能对比
        print("\n1. 检索性能对比")
        print("-" * 50)
        print(f"{'方法':<12} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'相似度':<8} {'答案质量':<8}")
        print("-" * 60)

        method_scores = []
        for method_name, method_data in results.items():
            if "average_metrics" in method_data:
                metrics = method_data["average_metrics"]
                quality = method_data.get("average_answer_quality", 0)

                print(f"{method_name:<12} {metrics['precision']:<8.3f} {metrics['recall']:<8.3f} "
                      f"{metrics['f1']:<8.3f} {metrics['similarity']:<8.3f} {quality:<8.3f}")

                method_scores.append({
                    "method": method_name,
                    "f1": metrics['f1'],
                    "quality": quality,
                    "combined_score": 0.6 * metrics['f1'] + 0.4 * quality
                })

        # 推荐最佳方法
        if method_scores:
            best_method = max(method_scores, key=lambda x: x["combined_score"])
            print(f"\n2. 推荐方法")
            print("-" * 50)
            print(f"最佳检索方法: {best_method['method']}")
            print(f"综合得分: {best_method['combined_score']:.3f}")
            print(f"F1分数: {best_method['f1']:.3f}")
            print(f"答案质量: {best_method['quality']:.3f}")

        # 改进建议
        print(f"\n3. 改进建议")
        print("-" * 50)

        if method_scores:
            avg_f1 = np.mean([m['f1'] for m in method_scores])
            avg_quality = np.mean([m['quality'] for m in method_scores])

            if avg_f1 < 0.5:
                print("• 检索性能较低，建议：")
                print("  - 优化知识库内容，确保覆盖测试查询")
                print("  - 调整相似度阈值")
                print("  - 改进查询重写策略")

            if avg_quality < 0.5:
                print("• 答案质量较低，建议：")
                print("  - 优化答案生成提示")
                print("  - 增加上下文信息")
                print("  - 改进检索结果排序")

        print("\n" + "=" * 80)

def main():
    """主函数：运行改进版RAG系统评估"""

    print("=" * 80)
    print("改进版RAG系统评估")
    print("=" * 80)

    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

    if not api_key:
        print("❌ 错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return

    try:
        # 初始化改进版RAG系统
        print("🚀 初始化改进版RAG系统...")
        rag_system = ImprovedRAGSystem(api_key)

        # 设置知识库（默认使用匹配的知识库）
        print("📚 设置知识库...")
        rag_system.setup_knowledge_base(use_matched_kb=True)

        # 选择运行模式
        print("\n选择运行模式:")
        print("1. 快速评估 - 使用默认设置")
        print("2. 详细评估 - 包含多阈值分析")
        print("3. 单方法测试 - 测试特定检索方法")

        choice = input("请选择模式 (1-3，默认为1): ").strip()

        if choice == "3":
            # 单方法测试
            print("\n可用的检索方法:")
            methods = ["vector", "bm25", "rrf", "hyde", "hybrid"]
            for i, method in enumerate(methods, 1):
                print(f"{i}. {method}")

            method_choice = input("请选择方法 (1-5，默认为5): ").strip()
            try:
                method_idx = int(method_choice) - 1 if method_choice else 4
                selected_method = methods[method_idx]
            except (ValueError, IndexError):
                selected_method = "hybrid"

            print(f"\n🔍 测试 {selected_method} 方法...")

            test_cases = rag_system.get_improved_test_cases()
            for i, test_case in enumerate(test_cases):
                query = test_case["query"]
                expected_answers = test_case["expected_answers"]

                print(f"\n查询 {i+1}: {query}")

                # 执行检索
                retrieved_docs = rag_system.improved_retrieve(query, method=selected_method, top_k=3)
                retrieved_texts = [doc['text'] for doc in retrieved_docs]

                # 生成答案
                generated_answer = rag_system.generate_answer(query, retrieved_docs)

                # 评估
                retrieval_eval = rag_system.evaluator.evaluate_single_query(
                    query, expected_answers, retrieved_texts, threshold=0.3
                )

                answer_eval = rag_system.evaluator.evaluate_generated_answer(
                    query, generated_answer, expected_answers
                )

                print(f"检索结果: {[text[:100] + '...' for text in retrieved_texts]}")
                print(f"生成答案: {generated_answer}")
                print(f"精确率: {retrieval_eval['precision']:.3f}")
                print(f"召回率: {retrieval_eval['recall']:.3f}")
                print(f"F1分数: {retrieval_eval['f1']:.3f}")
                print(f"答案质量: {answer_eval.get('quality_score', 0):.3f}")
                print("-" * 60)

        else:
            # 综合评估
            print("\n🔬 开始综合评估...")

            # 设置评估参数
            if choice == "2":
                # 详细评估
                print("使用详细评估模式（包含多阈值分析）")
            else:
                # 快速评估
                print("使用快速评估模式")

            # 执行评估
            results = rag_system.comprehensive_evaluation()

            # 保存结果
            results_file = 'local_data/improved_evaluation_results.json'
            os.makedirs('local_data', exist_ok=True)

            # 转换numpy类型为Python原生类型
            def convert_for_json(obj):
                if isinstance(obj, np.ndarray):
                    return obj.tolist()
                elif isinstance(obj, (np.float64, np.float32)):
                    return float(obj)
                elif isinstance(obj, (np.int64, np.int32)):
                    return int(obj)
                elif isinstance(obj, dict):
                    return {k: convert_for_json(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_for_json(item) for item in obj]
                else:
                    return obj

            converted_results = convert_for_json(results)

            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(converted_results, f, ensure_ascii=False, indent=2)

            print(f"\n💾 评估结果已保存到: {results_file}")

        print("\n✅ 评估完成！")

    except Exception as e:
        print(f"❌ 评估过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

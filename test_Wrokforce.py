from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType, TaskType
from camel.societies.workforce import Workforce
from camel.toolkits import SearchToolkit # 导入搜索工具
from camel.messages import BaseMessage
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN') # 或者你的其他API密钥

# 创建模型实例
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

#--------------------------------------------------
#定义工作节点 (Worker Agents)我们创建三个不同职责的 ChatAgent，每个 Agent 都有自己的系统消息和潜在的工具。
#--------------------------------------------------

# 1. 研究员 Agent：负责信息搜索
researcher_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="旅游信息研究员",
        content="你是一个专业的旅游信息研究员，精通网络搜索。你的职责是准确、全面地收集指定目的地的景点、美食、交通和住宿信息，并提供给规划师。你会使用搜索工具来获取最新和最准确的数据。"
    ),
    model=model,
    tools=[SearchToolkit().search_duckduckgo] # 赋予搜索能力
)

# 2. 规划师 Agent：负责行程规划
planner_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="高级旅游规划师",
        content="你是一位经验丰富的高级旅游规划师。你的职责是根据研究员提供的信息和用户的具体需求，制定一份合理、详细且富有吸引力的旅游行程。你需要考虑时间安排、交通便利性、景点特色和用户体验。"
    ),
    model=model
)

# 3. 评审员 Agent：负责评估和优化
reviewer_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="资深旅行评论员",
        content="你是一位眼光独到的资深旅行评论员。你的职责是像真正的旅行者一样，从实用性、趣味性、性价比和潜在问题等角度，严格评审规划师提供的旅游行程，并提出建设性的改进意见，确保行程质量。"
    ),
    model=model
)

print("✅ 所有工作节点（研究员、规划师、评审员）已定义。")

#--------------------------------------------------
#创建 Workforce 实例并添加工作节点
#--------------------------------------------------

from camel.tasks import Task # 导入Task类

# 创建协调器 Agent，使用相同的模型
coordinator_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="工作组协调员",
        content="你是一个高效的工作组协调员，负责协调各个专业团队成员的工作。你需要分析任务需求，合理分配工作给合适的团队成员，并确保工作流程顺畅进行。"
    ),
    model=model
)

# 创建任务代理 Agent，负责任务规划
from camel.toolkits import TaskPlanningToolkit
task_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="任务规划专家",
        content="你是一个专业的任务规划专家，负责将复杂任务分解为具体的子任务，并制定执行计划。你擅长分析任务需求，识别关键步骤，并合理安排任务执行顺序。"
    ),
    model=model,
    tools=TaskPlanningToolkit().get_tools()  # 添加任务规划工具
)

# 创建 Workforce 实例
# 这里的 description 会帮助 Coordinator Agent 理解整个工作组的职责
travel_workforce = Workforce(
    description="东京旅游行程规划与评估工作组",
    coordinator_agent=coordinator_agent,  # 使用我们自定义的协调器
    task_agent=task_agent  # 使用我们自定义的任务代理
)

# 添加工作节点到 Workforce
# 每个 add_single_agent_worker 都会注册一个工人智能体
travel_workforce.add_single_agent_worker(
    "负责搜索东京的景点、美食和交通信息", # 描述非常重要，协调器会根据这个来分配任务
    worker=researcher_agent
).add_single_agent_worker(
    "负责根据收集到的信息制定详细的东京3日旅游行程",
    worker=planner_agent
).add_single_agent_worker(
    "负责从游客角度评估旅游行程的合理性、趣味性，并提出改进建议",
    worker=reviewer_agent
)

print("✅ Workforce 实例已创建，并成功添加了所有工作节点。")

#--------------------------------------------------
#创建任务并启动 Workforce
#--------------------------------------------------

# 定义要处理的任务
# content 是给整个 Workforce 的主要指令
# additional_info 需要是字典格式，包含任务的背景信息或额外上下文
tokyo_task = Task(
    content="请为一位首次访问东京的游客规划一份详细的3日旅游行程。行程应包含热门景点、特色美食体验，并考虑交通便利性。",
    additional_info={
        "游客偏好": "希望体验东京的现代与传统结合，对购物和美食有兴趣",
        "当前日期": "2025年7月17日",
        "旅行天数": "3天",
        "游客类型": "首次访问东京的游客"
    },
    id="tokyo_trip_plan_001"
)

print(f"\n🚀 开始处理任务: '{tokyo_task.content[:50]}...'")

# 让 Workforce 处理这个任务
# 注意：在某些环境中（如 Jupyter Notebook），你可能需要运行 nest_asyncio.apply()
# import nest_asyncio
# nest_asyncio.apply()

# 这将启动多智能体协作流程
processed_task = travel_workforce.process_task(tokyo_task)

print("\n--- 任务处理完成 ---")
print(f"最终结果 (Task ID: {processed_task.id}):\n")
print(processed_task.result)

print("\n🎉 Workforce 模式演示成功！")
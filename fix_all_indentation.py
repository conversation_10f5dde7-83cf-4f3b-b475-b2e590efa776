#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
系统性修复所有缩进问题
"""

import re

def fix_all_indentation(file_path):
    """系统性修复文件的所有缩进问题"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    current_indent = 0
    in_class = False
    in_function = False
    in_multiline_string = False
    
    for i, line in enumerate(lines):
        original_line = line
        stripped = line.strip()
        
        # 跳过空行
        if not stripped:
            fixed_lines.append(line)
            continue
        
        # 检测多行字符串
        if '"""' in stripped:
            if in_multiline_string:
                in_multiline_string = False
            else:
                in_multiline_string = True
        
        # 如果在多行字符串中，保持原样
        if in_multiline_string and not stripped.startswith('"""'):
            fixed_lines.append(line)
            continue
        
        # 检测类定义
        if stripped.startswith('class '):
            current_indent = 0
            in_class = True
            in_function = False
            fixed_lines.append(line)
            continue
        
        # 检测函数定义
        if stripped.startswith('def '):
            if in_class:
                current_indent = 4
            else:
                current_indent = 0
            in_function = True
            # 确保函数定义有正确的缩进
            if in_class:
                fixed_lines.append('    ' + stripped + '\n')
            else:
                fixed_lines.append(stripped + '\n')
            continue
        
        # 检测装饰器
        if stripped.startswith('@'):
            if in_class:
                fixed_lines.append('    ' + stripped + '\n')
            else:
                fixed_lines.append(stripped + '\n')
            continue
        
        # 检测docstring
        if stripped.startswith('"""') and stripped.endswith('"""') and len(stripped) > 6:
            if in_function:
                fixed_lines.append('    ' * (current_indent // 4 + 2) + stripped + '\n')
            elif in_class:
                fixed_lines.append('    ' * (current_indent // 4 + 2) + stripped + '\n')
            else:
                fixed_lines.append(stripped + '\n')
            continue
        
        # 检测return, break, continue, pass等语句
        if stripped in ['pass', 'break', 'continue'] or stripped.startswith('return'):
            if in_function:
                fixed_lines.append('    ' * (current_indent // 4 + 2) + stripped + '\n')
            elif in_class:
                fixed_lines.append('    ' * (current_indent // 4 + 1) + stripped + '\n')
            else:
                fixed_lines.append(stripped + '\n')
            continue
        
        # 检测控制结构
        if any(stripped.startswith(keyword) for keyword in ['if ', 'elif ', 'else:', 'for ', 'while ', 'try:', 'except', 'finally:', 'with ']):
            if in_function:
                indent_level = current_indent // 4 + 2
            elif in_class:
                indent_level = current_indent // 4 + 1
            else:
                indent_level = 0
            fixed_lines.append('    ' * indent_level + stripped + '\n')
            continue
        
        # 检测普通语句
        if in_function:
            # 函数内的普通语句
            if stripped.startswith('self.') or '=' in stripped or stripped.startswith('print(') or stripped.startswith('raise '):
                fixed_lines.append('    ' * (current_indent // 4 + 2) + stripped + '\n')
            else:
                fixed_lines.append('    ' * (current_indent // 4 + 2) + stripped + '\n')
        elif in_class and not in_function:
            # 类内但不在函数内的语句
            fixed_lines.append('    ' + stripped + '\n')
        else:
            # 模块级别的语句
            fixed_lines.append(stripped + '\n')
        
        # 检测是否离开了函数或类
        if stripped.startswith('class ') or stripped.startswith('def ') or (not line.startswith(' ') and stripped and not stripped.startswith('#')):
            if not stripped.startswith('class ') and not stripped.startswith('def ') and not stripped.startswith('@'):
                in_function = False
                if not stripped.startswith('class '):
                    in_class = False
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"已修复 {file_path} 的所有缩进问题")

if __name__ == "__main__":
    fix_all_indentation("CAMEL_Prompt_task2_workforce.py")
    print("所有缩进问题修复完成！")
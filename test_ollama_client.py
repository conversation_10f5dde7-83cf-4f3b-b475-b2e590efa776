import ollama
import json
from datetime import datetime # 导入 datetime 模块

# 确认 Ollama 服务正在运行
print("--- 正在连接 Ollama 服务 ---")
try:
    models_response = ollama.list() # 这会返回一个 ListResponse 对象

    print("原始 Ollama.list() 响应对象类型:", type(models_response))

    # 将 ListResponse 对象转换为可序列化的字典
    # 转换方法取决于内部的具体实现，对于 Pydantic V2 模型通常是 model_dump()，
    # 对于旧的 Pydantic/对象可能是 dict()。
    # 我们会尝试常用的方法。如果一个失败，就尝试下一个。
    try:
        models_serializable = models_response.model_dump() # 适用于 Pydantic V2
    except AttributeError:
        try:
            models_serializable = models_response.dict() # 适用于旧的 Pydantic
        except AttributeError:
            # 如果以上都不行，可能是需要手动访问属性的特殊情况
            # 这种情况较少发生于标准库对象
            print("警告: 无法直接将 ListResponse 对象转换为字典。尝试直接访问属性。")
            # 假设 ListResponse 对象有一个名为 'models' 的属性，其中包含模型列表
            # 如果实际结构不同，这里可能需要根据实际情况调整
            models_serializable = {"models": models_response.models if hasattr(models_response, 'models') else []}

    print("序列化后的 Ollama.list() 响应:")
    # === 关键修改：为 json.dumps 添加 default 参数来处理 datetime 对象 ===
    def datetime_serializer(obj):
        if isinstance(obj, datetime):
            return obj.isoformat() # 将 datetime 对象转换为 ISO 格式字符串
        # 如果不是 datetime 类型，并且也不是可序列化的类型，抛出 TypeError
        raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

    # ensure_ascii=False 是为了让中文字符正常显示，而不是显示为 \uXXXX 编码
    print(json.dumps(models_serializable, indent=2, ensure_ascii=False, default=datetime_serializer))

    print("\n成功连接到 Ollama 服务。解析模型列表:")
    # 现在我们有了一个可序列化的字典，可以访问其中的 'models' 键
    if 'models' in models_serializable and models_serializable['models']:
        for model_info in models_serializable['models']:
            print(f"- {model_info.get('name', 'N/A')} (ID: {model_info.get('model', 'N/A')})")
    else:
        print("Ollama.list() 响应中没有 'models' 键，或者 'models' 列表为空。")

except Exception as e:
    print(f"无法连接到 Ollama 服务，或获取模型列表失败: {e}")
    print("请确保 Ollama 服务正在运行。")
    exit()

# 设置你想要测试的模型名称
model_name = "qwen2.5:7b"
print(f"\n--- 正在使用模型: {model_name} 进行对话 ---")

try:
    # 发送对话请求
    response1 = ollama.chat(
        model=model_name,
        messages=[{'role': 'user', 'content': '你好，你是谁？'}],
        options={
            "temperature": 0.4
        }
    )
    # 打印模型的回复
    if response1 and 'message' in response1 and 'content' in response1['message']:
        print(f"Qwen2.5: {response1['message']['content']}")
    else:
        print("Qwen2.5: 未收到有效回复。")

    print("\n--- 正在发送第二个对话请求 ---")
    # 第二次对话，测试模型的连续性
    # 确保正确的消息结构以保持上下文的连续对话
    messages_for_second_turn = [
        {'role': 'user', 'content': '你好，你是谁？'},
        {'role': 'assistant', 'content': response1['message']['content'] if response1 and 'message' in response1 else ''},
        {'role': 'user', 'content': '请给我讲一个关于人工智能的故事。'}
    ]

    response2 = ollama.chat(
        model=model_name,
        messages=messages_for_second_turn,
        options={
            "temperature": 0.4
        }
    )
    if response2 and 'message' in response2 and 'content' in response2['message']:
        print(f"Qwen2.5: {response2['message']['content']}")
    else:
        print("Qwen2.5: 未收到有效回复。")

except Exception as e:
    print(f"\n与 Ollama 模型交互时发生错误: {e}")
    print("请检查：")
    print(f"1. '{model_name}' 模型是否已成功拉取 (运行 'ollama list')。")
    print("2. Ollama 服务是否正在运行。")
    print("3. Python 环境是否安装了所有必要的库 (ollama 库)。")

print("\n--- 测试脚本执行完毕 ---")
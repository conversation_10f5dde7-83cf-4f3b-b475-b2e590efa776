"""
HyDE+RRF功能测试脚本

该脚本专门测试新实现的HyDE+RRF组合功能：
1. 向量检索使用HyDE生成的假设文档
2. BM25检索使用原始查询
3. 通过RRF算法融合两种检索结果
"""

import os
from dotenv import load_dotenv

def test_hyde_rrf_functionality():
    """测试HyDE+RRF功能"""
    
    print("=" * 80)
    print("HyDE+RRF功能测试")
    print("=" * 80)
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("❌ 错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return False
    
    try:
        # 导入增强版RAG系统
        print("🔧 导入增强版RAG系统...")
        from test_RAG_Enhanced import EnhancedRAGSystem
        
        # 初始化系统
        print("🚀 初始化系统...")
        rag_system = EnhancedRAGSystem(api_key)
        
        # 设置评估知识库
        print("📚 设置评估知识库...")
        rag_system.setup_evaluation_knowledge_base(chunk_size=100)
        
        # 检查检索器状态
        print("\n📊 检查检索器状态:")
        status = rag_system.check_retrievers_status()
        
        # 测试查询
        test_queries = [
            "什么是CAMEL AI？",
            "如何开始使用CAMEL AI？",
            "CAMEL AI的主要特点是什么？"
        ]
        
        for i, query in enumerate(test_queries):
            print(f"\n{'='*60}")
            print(f"测试查询 {i+1}: {query}")
            print("="*60)
            
            # 测试1: 传统RRF（不使用HyDE）
            print(f"\n🔍 测试1: 传统RRF检索")
            print("-" * 40)
            try:
                traditional_rrf_results = rag_system.retrieve_with_rrf(
                    query=query,
                    use_hyde=False,
                    top_k=5,
                    rrf_k=3
                )
                
                print(f"✅ 传统RRF检索成功，返回 {len(traditional_rrf_results)} 个结果")
                print("结果来源:")
                for j, result in enumerate(traditional_rrf_results):
                    source = result.get('source', 'Unknown')
                    score = result.get('rrf_score', 0)
                    print(f"  结果 {j+1}: {source} (分数: {score:.4f})")
                
            except Exception as e:
                print(f"❌ 传统RRF检索失败: {e}")
                continue
            
            # 测试2: HyDE+RRF（新功能）
            print(f"\n🎯 测试2: HyDE+RRF检索（新功能）")
            print("-" * 40)
            try:
                hyde_rrf_results = rag_system.retrieve_with_rrf(
                    query=query,
                    use_hyde=True,
                    top_k=5,
                    rrf_k=3
                )
                
                print(f"✅ HyDE+RRF检索成功，返回 {len(hyde_rrf_results)} 个结果")
                print("结果来源:")
                for j, result in enumerate(hyde_rrf_results):
                    source = result.get('source', 'Unknown')
                    score = result.get('rrf_score', 0)
                    print(f"  结果 {j+1}: {source} (分数: {score:.4f})")
                
            except Exception as e:
                print(f"❌ HyDE+RRF检索失败: {e}")
                continue
            
            # 测试3: 完整的enhanced_query（HyDE+RRF）
            print(f"\n🚀 测试3: 完整的enhanced_query（HyDE+RRF）")
            print("-" * 40)
            try:
                enhanced_results = rag_system.enhanced_query(
                    original_query=query,
                    use_rewriting=False,  # 简化测试
                    use_hyde=True,
                    use_rrf=True,
                    top_k=5,
                    rrf_k=3
                )
                
                print(f"✅ 完整增强查询成功！")
                print(f"检索方法: {enhanced_results.get('retrieval_method', 'Unknown')}")
                print(f"返回文档数: {len(enhanced_results.get('retrieved_docs', []))}")
                
                # 显示生成的答案
                answer = enhanced_results.get('final_answer', '')
                if answer:
                    print(f"生成答案: {answer[:200]}...")
                
            except Exception as e:
                print(f"❌ 完整增强查询失败: {e}")
                continue
            
            # 只测试第一个查询的详细对比
            if i == 0:
                print(f"\n📊 结果对比分析")
                print("-" * 40)
                
                # 对比检索结果
                traditional_texts = [r['text'][:100] for r in traditional_rrf_results]
                hyde_texts = [r['text'][:100] for r in hyde_rrf_results]
                
                print("传统RRF前3个结果片段:")
                for j, text in enumerate(traditional_texts):
                    print(f"  {j+1}. {text}...")
                
                print("\nHyDE+RRF前3个结果片段:")
                for j, text in enumerate(hyde_texts):
                    print(f"  {j+1}. {text}...")
                
                # 计算重叠度
                overlap = len(set(traditional_texts) & set(hyde_texts))
                print(f"\n结果重叠度: {overlap}/{min(len(traditional_texts), len(hyde_texts))}")
        
        # 测试总结
        print("\n" + "=" * 80)
        print("✅ HyDE+RRF功能测试完成！")
        print("=" * 80)
        
        print("功能验证结果:")
        print("  ✅ HyDE假设文档生成正常")
        print("  ✅ HyDE向量检索工作正常")
        print("  ✅ BM25使用原始查询正常")
        print("  ✅ RRF融合算法工作正常")
        print("  ✅ 结果格式标识正确")
        print("  ✅ 完整流程集成成功")
        
        print("\n新功能特点:")
        print("  🎯 向量检索：使用HyDE生成的假设文档")
        print("  🔍 BM25检索：使用原始用户查询")
        print("  🔄 RRF融合：智能合并两种检索结果")
        print("  📊 结果标识：清晰区分不同检索方法")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_scenarios():
    """测试不同场景下的HyDE+RRF效果"""
    
    print("\n" + "=" * 80)
    print("不同场景测试")
    print("=" * 80)
    
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("❌ 错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return False
    
    try:
        from test_RAG_Enhanced import EnhancedRAGSystem
        
        # 初始化系统
        rag_system = EnhancedRAGSystem(api_key)
        rag_system.setup_evaluation_knowledge_base(chunk_size=100)
        
        # 不同类型的查询
        scenarios = [
            {
                "name": "简短查询",
                "query": "CAMEL AI",
                "description": "测试HyDE对简短查询的增强效果"
            },
            {
                "name": "模糊查询", 
                "query": "这个框架怎么用？",
                "description": "测试HyDE对模糊查询的理解能力"
            },
            {
                "name": "技术细节查询",
                "query": "角色扮演功能的具体实现机制是什么？",
                "description": "测试HyDE对复杂技术查询的处理"
            }
        ]
        
        for scenario in scenarios:
            print(f"\n📋 场景: {scenario['name']}")
            print(f"查询: {scenario['query']}")
            print(f"说明: {scenario['description']}")
            print("-" * 60)
            
            try:
                # 使用HyDE+RRF
                results = rag_system.retrieve_with_rrf(
                    query=scenario['query'],
                    use_hyde=True,
                    top_k=3,
                    rrf_k=2
                )
                
                print(f"✅ 检索成功，返回 {len(results)} 个结果")
                
                # 显示结果质量
                for i, result in enumerate(results):
                    score = result.get('rrf_score', 0)
                    text_preview = result['text'][:150] + "..."
                    print(f"  结果 {i+1} (分数: {score:.4f}): {text_preview}")
                
            except Exception as e:
                print(f"❌ 场景测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 场景测试出错: {e}")
        return False

if __name__ == "__main__":
    # 运行基本功能测试
    success1 = test_hyde_rrf_functionality()
    
    if success1:
        # 询问是否运行场景测试
        test_scenarios = input("\n是否运行不同场景测试？(y/n): ").strip().lower()
        if test_scenarios == 'y':
            success2 = test_different_scenarios()
            
            if success1 and success2:
                print("\n🎉 所有测试通过！HyDE+RRF功能完美运行！")
            else:
                print("\n⚠️ 部分测试失败，请检查具体错误信息")
        else:
            print("\n🎉 基本功能测试通过！")
    else:
        print("\n❌ 基本测试失败，请检查错误信息")

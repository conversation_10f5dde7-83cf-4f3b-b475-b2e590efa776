from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType, TaskType
from camel.societies.workforce import Workforce
from camel.toolkits import SearchToolkit
from camel.messages import BaseMessage
from camel.tasks import Task
import os
import time
import asyncio
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 创建模型实例
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

print("🔧 正在创建带AI用户的智能 Workforce...")

#--------------------------------------------------
# 自定义搜索工具类，添加速率限制控制
#--------------------------------------------------

class RateLimitedSearchToolkit:
    """带速率限制的搜索工具包"""
    
    def __init__(self, delay_seconds=3):
        self.search_toolkit = SearchToolkit()
        self.delay_seconds = delay_seconds
        self.last_search_time = 0
    
    def safe_search_duckduckgo(self, query: str, max_results: int = 5):
        """安全的搜索方法，包含速率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_search_time
        
        if time_since_last < self.delay_seconds:
            sleep_time = self.delay_seconds - time_since_last
            print(f"⏳ 等待 {sleep_time:.1f} 秒以避免速率限制...")
            time.sleep(sleep_time)
        
        try:
            result = self.search_toolkit.search_duckduckgo(query, max_results)
            self.last_search_time = time.time()
            print(f"✅ 搜索成功: {query[:50]}...")
            return result
        except Exception as e:
            print(f"⚠️ 搜索失败: {str(e)}")
            return f"搜索暂时不可用，请使用已有知识回答。错误: {str(e)}"

# 创建速率限制搜索工具实例
safe_search = RateLimitedSearchToolkit(delay_seconds=5)

#--------------------------------------------------
# 定义工作节点（包含AI用户Agent）
#--------------------------------------------------

# 1. AI用户 Agent：模拟真实用户，提出个性化需求
ai_user_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="AI智能用户",
        content="""你是一个智能的AI用户代理，代表真实用户参与旅游规划过程。你的职责是：

        🎯 核心任务：
        1. 根据基础需求，深入挖掘和表达个性化旅游偏好
        2. 提出具体的、有个性的旅游要求和限制条件
        3. 在规划过程中提供反馈和调整建议
        4. 确保最终行程符合用户的真实需求

        👤 用户画像（你需要扮演的角色）：
        - 25-30岁的年轻专业人士
        - 首次访问东京，对日本文化很感兴趣
        - 喜欢拍照分享到社交媒体
        - 预算中等，注重性价比
        - 不喜欢过于拥挤的地方
        - 对动漫文化有一定兴趣
        - 希望体验地道的日本生活

        💡 个性化需求示例：
        - "我希望能找到一些小众但很有特色的咖啡店"
        - "我不太喜欢太早起床，希望行程从上午10点开始"
        - "我想体验一次传统的日式早餐"
        - "希望能安排一些适合拍照的网红打卡点"
        - "我对寺庙很感兴趣，但不想走太多路"

        📝 交互方式：
        - 主动提出具体的个性化需求
        - 对规划师的建议给出反馈
        - 提出实际的担忧和问题
        - 表达对某些活动的偏好程度"""
    ),
    model=model
)

# 2. 研究员 Agent：使用安全搜索工具
researcher_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="东京旅游信息研究员",
        content="""你是一个专业的东京旅游信息研究员。你的职责是：
        1. 使用搜索工具获取最新的东京旅游信息
        2. 特别关注AI用户提出的个性化需求相关信息
        3. 搜索小众景点、特色咖啡店、网红打卡点等
        4. 如果搜索不可用，使用你的专业知识提供信息
        5. 重点关注：热门景点、特色美食、交通方式、购物场所
        
        🔍 搜索重点：
        - 根据AI用户的个性化需求调整搜索内容
        - 寻找适合拍照的地点和时间
        - 查找小众但有特色的场所
        - 获取最新的营业时间和价格信息"""
    ),
    model=model,
    tools=[safe_search.safe_search_duckduckgo]
)

# 3. 智能规划师 Agent：整合AI用户需求
intelligent_planner_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="智能旅游规划师",
        content="""你是一位高级智能旅游规划师，擅长个性化定制。你的职责是：

        🎯 核心能力：
        1. 深度理解AI用户的个性化需求和偏好
        2. 将用户需求与研究员提供的信息完美结合
        3. 制定高度个性化的旅游行程
        4. 考虑用户的生活习惯和个人限制

        📋 规划原则：
        - 优先满足AI用户的个性化需求
        - 平衡热门景点与小众体验
        - 考虑用户的作息时间偏好
        - 安排适合拍照的时间和地点
        - 包含地道的日本文化体验
        - 确保交通便利和时间合理

        🔄 互动方式：
        - 主动询问AI用户的具体偏好
        - 根据用户反馈调整行程安排
        - 提供多个选择方案供用户选择
        - 解释每个安排的理由和优势

        📝 输出格式：
        - 详细的每日行程安排
        - 个性化推荐理由
        - 实用的交通和时间信息
        - 拍照和体验建议"""
    ),
    model=model
)

# 4. 评审员 Agent
reviewer_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="资深旅行评论员",
        content="""你是一位资深的旅行评论员，特别关注个性化体验。你的职责是：
        1. 评估行程是否真正满足AI用户的个性化需求
        2. 从实际可行性角度审查行程安排
        3. 识别潜在问题和改进空间
        4. 确保行程的独特性和实用性并存
        
        🔍 评估重点：
        - 个性化需求的满足程度
        - 行程的独特性和创新性
        - 实际执行的可行性
        - 时间和预算的合理性
        - 用户体验的连贯性"""
    ),
    model=model
)

print("✅ 所有工作节点已定义（包含AI用户Agent）")

#--------------------------------------------------
# 创建增强版 Workforce 实例
#--------------------------------------------------

# 协调器 Agent - 增强版
coordinator_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="智能工作组协调员",
        content="""你是智能旅游规划工作组的高级协调员。你的特殊职责是：
        1. 协调AI用户与各专业团队的沟通
        2. 确保用户的个性化需求被充分理解和满足
        3. 管理多轮交互和反馈循环
        4. 平衡用户需求与专业建议
        5. 确保最终输出高度个性化且实用可行"""
    ),
    model=model
)

# 任务代理 Agent
from camel.toolkits import TaskPlanningToolkit
task_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="个性化任务规划专家",
        content="你是个性化任务规划专家，负责将复杂的个性化旅游规划任务分解为可执行的子任务，特别注重用户需求的传递和整合。"
    ),
    model=model,
    tools=TaskPlanningToolkit().get_tools()
)

# 创建增强版 Workforce
travel_workforce = Workforce(
    description="东京个性化旅游规划工作组（包含AI用户交互）",
    coordinator_agent=coordinator_agent,
    task_agent=task_agent
)

# 添加工作节点 - 注意顺序很重要
travel_workforce.add_single_agent_worker(
    "AI用户代理：表达个性化旅游需求和偏好，提供用户反馈",
    worker=ai_user_agent
).add_single_agent_worker(
    "旅游信息研究员：根据AI用户需求搜索相关的最新旅游信息",
    worker=researcher_agent
).add_single_agent_worker(
    "智能规划师：整合用户需求和研究信息，制定个性化行程",
    worker=intelligent_planner_agent
).add_single_agent_worker(
    "资深评论员：评估个性化行程的质量并提出优化建议",
    worker=reviewer_agent
)

print("✅ 增强版 Workforce 实例已创建（包含AI用户交互）")

#--------------------------------------------------
# 创建和处理个性化任务
#--------------------------------------------------

personalized_tokyo_task = Task(
    content="""请为一位首次访问东京的年轻专业人士规划一份高度个性化的3日旅游行程。

    🎯 任务要求：
    1. AI用户代理需要首先表达具体的个性化需求和偏好
    2. 研究员根据这些需求搜索相关信息
    3. 规划师整合用户需求制定个性化行程
    4. 评审员确保行程满足用户的个性化要求

    💡 期望的个性化元素：
    - 考虑用户的生活习惯和偏好
    - 包含独特的、非标准化的体验
    - 平衡热门景点与小众发现
    - 适合社交媒体分享的元素
    - 地道的日本文化体验""",
    
    additional_info={
        "基础用户画像": "25-30岁年轻专业人士，首次访问东京",
        "基础偏好": "对日本文化感兴趣，喜欢拍照分享",
        "当前日期": "2025年7月17日",
        "旅行天数": "3天",
        "预算水平": "中等预算，注重性价比",
        "特殊要求": "需要AI用户深度参与，提出个性化需求",
        "期望结果": "高度个性化且实用的旅游行程"
    },
    id="personalized_tokyo_trip_with_ai_user"
)

print(f"\n🚀 开始处理个性化任务（AI用户参与）...")
print(f"任务: {personalized_tokyo_task.content[:100]}...")

try:
    print("\n⏳ 正在启动AI用户交互式规划流程...")
    print("📱 AI用户将首先表达个性化需求...")
    print("🔍 研究员将根据需求搜索信息...")
    print("📋 规划师将制定个性化行程...")
    print("✅ 评审员将确保质量...")
    
    # 处理任务
    processed_task = travel_workforce.process_task(personalized_tokyo_task)
    
    print("\n" + "="*90)
    print("🎯 个性化任务处理完成 - AI用户交互版本")
    print("="*90)
    print(f"任务ID: {processed_task.id}")
    print(f"\n🌟 最终个性化行程:")
    print("-" * 80)
    print(processed_task.result)
    print("-" * 80)
    print("\n🎉 AI用户交互式 Workforce 演示成功！")
    print("\n💡 特色：")
    print("   ✨ AI用户主动表达个性化需求")
    print("   🔍 研究员针对性搜索相关信息")
    print("   📋 规划师深度整合用户偏好")
    print("   ✅ 评审员确保个性化质量")
    
except Exception as e:
    print(f"\n❌ 任务处理出现错误: {str(e)}")
    print("可能的原因：")
    print("1. 网络连接问题")
    print("2. 搜索服务速率限制")
    print("3. 模型配置问题")
    print("4. AI用户交互复杂度过高")
    print("\n💡 建议：可以尝试简化用户需求或使用基础版本")
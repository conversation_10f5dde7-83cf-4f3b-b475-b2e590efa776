from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from camel.messages import BaseMessage

from io import BytesIO
import requests
from PIL import Image

from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv(dotenv_path='.env')  # 确保.env文件存在且路径正确
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')  # 使用环境变量获取密钥

model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-VL-72B-Instruct",
    api_key=api_key,
    url=os.environ.get("OPENAI_COMPATIBILIY_API_BASE_URL", "https://api-inference.modelscope.cn/v1/"),  # 环境变量优先，不存在则使用默认值
    model_config_dict={"temperature": 0.4, "max_tokens": 4096},
)

# 实例化ChatAgent
agent = ChatAgent(model=model,output_language='中文')

# 本地图片
image_path = "微信图片_2025-07-11_223853_723.jpg"
img = Image.open(image_path)  # 直接打开图片文件

user_msg = BaseMessage.make_user_message(
    role_name="User", 
    content="请描述这张图片的内容", 
    image_list=[img]  # 使用image_list参数传入PIL.Image对象列表
)

response = agent.step(user_msg)   # 1. 将包含图片的用户消息发送给AI代理
print(response.msgs[0].content)  # 2. 打印AI代理的回复内容


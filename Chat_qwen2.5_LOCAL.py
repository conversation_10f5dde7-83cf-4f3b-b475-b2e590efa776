from camel.agents import Chat<PERSON>gent
from camel.models import ModelFactory
from camel.types import ModelPlatformType
import sys
from camel.messages import BaseMessage


model = ModelFactory.create(
    model_platform=ModelPlatformType.OLLAMA,
    model_type="qwen2.5:7b",  # Use the model name displayed in 'ollama list'
    model_config_dict={
        "temperature": 0.4,
    },
    url="http://localhost:11434/v1/"
)

agent = ChatAgent(
    model=model,
    output_language='中文'
)

print("与 CAMEL 代理开始对话 (输入 '退出' 结束):")

while True:
    print("你: (输入多行内容，空行结束)")
    user_input_lines = []
    while True:
        line = input()
        if line.lower() == '退出':
            print("对话结束。")
            sys.exit()  # 完全退出程序
        if not line.strip():  # 空行结束输入
            break
        user_input_lines.append(line)
    
    if user_input_lines:  # 如果有输入内容
        user_input = '\n'.join(user_input_lines)
    else:
        continue  # 空输入则重新开始循环

    # Create a BaseMessage from user input
    user_msg = BaseMessage.make_user_message(
        role_name="User",
        content=user_input
    )   

    # Get model response
    response = agent.step(user_msg)
    if response and response.msgs:
        print(f"代理: {response.msgs[0].content}")
    else:
        print("代理: 未收到有效回复。")

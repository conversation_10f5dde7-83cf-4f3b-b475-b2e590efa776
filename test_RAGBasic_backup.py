import os
import requests

os.makedirs('local_data', exist_ok=True)

url = "https://arxiv.org/pdf/2303.17760.pdf"
response = requests.get(url)
with open('local_data/camel_paper.pdf', 'wb') as file:
    file.write(response.content)

from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever

embedding_model=SentenceTransformerEncoder(model_name='intfloat/e5-large-v2')


# 创建并初始化一个向量数据库 (以QdrantStorage为例)
from camel.storages.vectordb_storages import QdrantStorage

vector_storage = QdrantStorage(
    vector_dim=embedding_model.get_output_dim(),
    collection="demo_collection",
    path="storage_customized_run",
    collection_name="论文"
)
# 初始化VectorRetriever实例并使用本地模型作为嵌入模型
vr = VectorRetriever(embedding_model= embedding_model,storage=vector_storage)
# 将文件读取、切块、嵌入并储存在向量数据库中，这大概需要1-2分钟
pdf_processed_successfully = False

try:
    print("正在处理PDF文件...")
    vr.process(content="local_data/camel_paper.pdf")
    print("PDF文件处理完成")
    
    # 检查是否真的有数据被添加
    try:
        # 尝试一个简单查询来检查数据库是否为空
        test_results = vr.query(query="test", top_k=1)
        if test_results:
            print("PDF内容已成功添加到向量数据库")
            pdf_processed_successfully = True
        else:
            print("警告：PDF处理完成但没有提取到内容")
    except:
        print("警告：PDF处理完成但向量数据库为空")
        
except Exception as e:
    print(f"PDF处理失败: {e}")

# 如果PDF处理失败或没有内容，使用备用方案
if not pdf_processed_successfully:
    print("使用文本内容作为替代...")
    

    try:
        # 使用VectorRetriever的process方法处理文本
        for i, text in enumerate(sample_texts):
            # 创建临时文本文件
            temp_file = f"temp_text_{i}.txt"
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(text)
            
            # 处理文本文件
            vr.process(content=temp_file)
            
            # 删除临时文件
            os.remove(temp_file)
        
        print("示例文本已添加到向量数据库")
        
    except Exception as text_e:
        print(f"文本处理失败: {text_e}")
        


# 设定一个查询语句
query = "CAMEL是什么"

# 执行查询并获取结果
try:
    print(f"正在查询: {query}")
    results = vr.query(query=query, top_k=1)
    print("查询结果:")
    print(results)
except Exception as e:
    print(f"查询失败: {e}")
    print("请确保:")
    print("1. 已安装PDF处理依赖: pip install 'unstructured[pdf]'")
    print("2. 向量数据库中有数据")
    print("3. 网络连接正常")
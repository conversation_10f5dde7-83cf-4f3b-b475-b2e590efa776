"""
BM25初始化修复验证脚本

该脚本用于验证BM25检索器初始化问题的修复效果
"""

import os
from dotenv import load_dotenv

# 导入修复后的增强版RAG系统
from test_RAG_Enhanced import EnhancedRAGSystem

def test_bm25_initialization():
    """测试BM25初始化修复"""
    
    print("=" * 80)
    print("BM25初始化修复验证测试")
    print("=" * 80)
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("❌ 错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return
    
    try:
        # 测试1: 初始化系统
        print("\n🚀 测试1: 初始化系统")
        print("-" * 40)
        rag_system = EnhancedRAGSystem(api_key)
        
        # 检查初始状态
        print("初始检索器状态:")
        initial_status = rag_system.check_retrievers_status()
        
        # 测试2: 设置评估知识库
        print("\n📚 测试2: 设置评估知识库")
        print("-" * 40)
        rag_system.setup_evaluation_knowledge_base(chunk_size=100)
        
        # 检查设置后的状态
        print("\n设置后检索器状态:")
        after_setup_status = rag_system.check_retrievers_status()
        
        # 测试3: 测试RRF检索（之前会失败的地方）
        print("\n🔍 测试3: 测试RRF检索")
        print("-" * 40)
        
        test_query = "什么是CAMEL AI？"
        print(f"测试查询: {test_query}")
        
        try:
            rrf_results = rag_system.retrieve_with_rrf(
                query=test_query,
                top_k=5,
                rrf_k=3
            )
            
            print(f"✅ RRF检索成功！返回 {len(rrf_results)} 个结果")
            
            # 显示结果摘要
            for i, result in enumerate(rrf_results):
                source = result.get('source', 'Unknown')
                score = result.get('rrf_score', 0)
                print(f"  结果 {i+1}: {source} (分数: {score:.4f})")
            
        except Exception as e:
            print(f"❌ RRF检索失败: {e}")
            return False
        
        # 测试4: 测试完整的enhanced_query
        print("\n🎯 测试4: 测试完整的enhanced_query")
        print("-" * 40)
        
        try:
            enhanced_results = rag_system.enhanced_query(
                original_query=test_query,
                use_rewriting=True,
                use_hyde=True,
                use_rrf=True,
                top_k=5,
                rrf_k=3
            )
            
            print(f"✅ 完整增强查询成功！")
            print(f"检索方法: {enhanced_results.get('retrieval_method', 'Unknown')}")
            print(f"返回文档数: {len(enhanced_results.get('retrieved_docs', []))}")
            
        except Exception as e:
            print(f"❌ 完整增强查询失败: {e}")
            return False
        
        # 测试5: 测试可用检索方法
        print("\n📋 测试5: 检查可用检索方法")
        print("-" * 40)
        
        available_methods = rag_system.get_available_retrieval_methods()
        print(f"可用检索方法: {available_methods}")
        
        # 测试总结
        print("\n" + "=" * 80)
        print("测试总结")
        print("=" * 80)
        
        print("✅ 所有测试通过！BM25初始化问题已修复")
        print("\n修复效果:")
        print("  • BM25初始化失败时系统不会崩溃")
        print("  • RRF检索会自动降级到向量检索")
        print("  • 提供清晰的状态信息和错误提示")
        print("  • 系统保持完整功能可用性")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_camel_paper():
    """测试使用CAMEL论文的场景（原始错误场景）"""
    
    print("\n" + "=" * 80)
    print("CAMEL论文场景测试（原始错误场景）")
    print("=" * 80)
    
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("❌ 错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return False
    
    try:
        # 初始化系统
        print("🚀 初始化系统...")
        rag_system = EnhancedRAGSystem(api_key)
        
        # 设置知识库（使用CAMEL论文）
        print("📚 设置知识库（CAMEL论文）...")
        pdf_url = "https://arxiv.org/pdf/2303.17760.pdf"
        rag_system.setup_knowledge_base(pdf_url=pdf_url)
        
        # 检查状态
        print("\n检索器状态:")
        rag_system.check_retrievers_status()
        
        # 测试原始会失败的查询
        print("\n🔍 测试原始会失败的查询...")
        test_query = "CAMEL是什么东东？它有什么特点吗"
        
        try:
            results = rag_system.enhanced_query(
                original_query=test_query,
                use_rewriting=True,
                use_hyde=True,
                use_rrf=True,
                top_k=10,
                rrf_k=3
            )
            
            print(f"✅ 查询成功！检索方法: {results.get('retrieval_method', 'Unknown')}")
            return True
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ CAMEL论文测试失败: {e}")
        return False

if __name__ == "__main__":
    # 运行基本修复验证
    success1 = test_bm25_initialization()
    
    if success1:
        # 如果基本测试通过，询问是否测试CAMEL论文场景
        test_camel = input("\n是否测试CAMEL论文场景？(y/n): ").strip().lower()
        if test_camel == 'y':
            success2 = test_with_camel_paper()
            
            if success1 and success2:
                print("\n🎉 所有测试通过！BM25初始化问题完全修复！")
            else:
                print("\n⚠️ 部分测试失败，请检查具体错误信息")
        else:
            print("\n🎉 基本修复验证通过！")
    else:
        print("\n❌ 基本测试失败，请检查修复代码")

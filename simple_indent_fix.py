#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的缩进修复 - 只修复明显的问题
"""

def simple_indent_fix(file_path):
    """简单修复明显的缩进问题"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复明显的缩进问题
    fixes = [
        # 修复函数和类内部的缩进
        ('\n def ', '\n    def '),
        ('\n @', '\n    @'),
        ('\n if ', '\n        if '),
        ('\n for ', '\n        for '),
        ('\n while ', '\n        while '),
        ('\n try:', '\n        try:'),
        ('\n except', '\n        except'),
        ('\n return ', '\n        return '),
        ('\n self.', '\n        self.'),
        ('\n print(', '\n        print('),
        ('\n raise ', '\n        raise '),
        ('\n # ', '\n        # '),
        
        # 修复类级别的缩进
        ('\nclass ', '\n\nclass '),
        ('\ndef ', '\n\ndef '),
    ]
    
    for old, new in fixes:
        content = content.replace(old, new)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已简单修复 {file_path} 的缩进问题")

if __name__ == "__main__":
    simple_indent_fix("CAMEL_Prompt_task2_workforce.py")
    print("简单缩进修复完成！")
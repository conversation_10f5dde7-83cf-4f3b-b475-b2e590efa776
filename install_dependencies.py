"""
RAG系统依赖安装脚本

该脚本帮助安装RAG系统所需的可选依赖包。
根据需要选择安装不同的功能模块。
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """安装单个包"""
    try:
        print(f"正在安装 {package_name}...")
        if description:
            print(f"  用途: {description}")
        
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出现未知错误: {e}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("RAG系统依赖安装向导")
    print("=" * 60)
    
    # 定义可选依赖包
    optional_packages = {
        "基础功能": [
            ("sentence-transformers", "语义相似度计算，提升文本匹配准确性"),
        ],
        "评估功能": [
            ("nltk", "BLEU分数计算，评估生成答案质量"),
            ("rouge-score", "ROUGE分数计算，评估文本摘要质量"),
        ],
        "可视化功能": [
            ("matplotlib", "绘制图表和可视化分析"),
            ("seaborn", "高级统计图表绘制"),
        ],
        "高级功能": [
            ("plotly", "交互式图表（可选）"),
            ("wordcloud", "词云生成（可选）"),
        ]
    }
    
    print("可用的功能模块:")
    for i, (category, packages) in enumerate(optional_packages.items(), 1):
        print(f"{i}. {category}")
        for package, desc in packages:
            status = "✅ 已安装" if check_package(package.split('[')[0]) else "❌ 未安装"
            print(f"   - {package}: {desc} [{status}]")
        print()
    
    print("安装选项:")
    print("1. 安装基础功能（推荐）")
    print("2. 安装评估功能")
    print("3. 安装可视化功能")
    print("4. 安装所有功能")
    print("5. 自定义安装")
    print("6. 只检查当前状态")
    print("0. 退出")
    
    choice = input("\n请选择安装选项 (0-6): ").strip()
    
    if choice == "0":
        print("退出安装程序")
        return
    
    elif choice == "6":
        print("\n当前依赖状态检查:")
        all_packages = []
        for category, packages in optional_packages.items():
            all_packages.extend(packages)
        
        for package, desc in all_packages:
            package_name = package.split('[')[0]
            status = "✅ 已安装" if check_package(package_name) else "❌ 未安装"
            print(f"{package_name}: {status}")
        return
    
    # 确定要安装的包
    packages_to_install = []
    
    if choice == "1":
        packages_to_install = optional_packages["基础功能"]
    elif choice == "2":
        packages_to_install = optional_packages["评估功能"]
    elif choice == "3":
        packages_to_install = optional_packages["可视化功能"]
    elif choice == "4":
        for packages in optional_packages.values():
            packages_to_install.extend(packages)
    elif choice == "5":
        print("\n自定义安装:")
        for category, packages in optional_packages.items():
            print(f"\n{category}:")
            for package, desc in packages:
                package_name = package.split('[')[0]
                if check_package(package_name):
                    print(f"  {package}: {desc} [已安装，跳过]")
                else:
                    install_choice = input(f"  安装 {package}? (y/n): ").strip().lower()
                    if install_choice in ['y', 'yes']:
                        packages_to_install.append((package, desc))
    else:
        print("无效选择")
        return
    
    if not packages_to_install:
        print("没有需要安装的包")
        return
    
    # 开始安装
    print(f"\n开始安装 {len(packages_to_install)} 个包...")
    print("=" * 60)
    
    success_count = 0
    failed_packages = []
    
    for package, desc in packages_to_install:
        package_name = package.split('[')[0]
        
        # 检查是否已安装
        if check_package(package_name):
            print(f"⏭️  {package_name} 已安装，跳过")
            success_count += 1
            continue
        
        # 安装包
        if install_package(package, desc):
            success_count += 1
        else:
            failed_packages.append(package)
        print()
    
    # 安装总结
    print("=" * 60)
    print("安装总结:")
    print(f"✅ 成功安装: {success_count}/{len(packages_to_install)}")
    
    if failed_packages:
        print(f"❌ 安装失败: {len(failed_packages)}")
        print("失败的包:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 尝试手动安装: pip install <package_name>")
        print("3. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple <package_name>")
    
    # 特殊说明
    if any("nltk" in pkg[0] for pkg in packages_to_install):
        print("\n📝 NLTK 特殊说明:")
        print("首次使用NLTK时，可能需要下载数据包:")
        print("import nltk")
        print("nltk.download('punkt')")
    
    print("\n🎉 安装完成！")
    print("\n可用的脚本:")
    print("- test_RAG_Simple_Improved.py: 简化版（无额外依赖）")
    print("- test_RAG_Enhanced_Improved.py: 完整版（需要额外依赖）")
    print("- test_RAG_Improvements_Demo.py: 改进效果演示")

if __name__ == "__main__":
    main()

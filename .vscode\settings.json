{"workbench.colorTheme": "Default Light Modern", "marscode.chatLanguage": "cn", "debug.onTaskErrors": "debugAnyway", "redhat.telemetry.enabled": true, "cmake.pinnedCommands": ["workbench.action.tasks.configureTaskRunner", "workbench.action.tasks.runTask"], "editor.codeActionsOnSave": {}, "cmake.showOptionsMovedNotification": false, "trae.chatLanguage": "cn", "trae.enableCodelens": {"enableInlineExplain": false, "enableInlineUnitTest": false, "enableInlineDocumentation": false}, "database-client.autoSync": true, "git.openRepositoryInParentFolders": "always", "python.defaultInterpreterPath": "./venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "marscode.enableCodelens": {"enableInlineDocumentation": false, "enableInlineUnitTest": false, "enableInlineExplain": false}}
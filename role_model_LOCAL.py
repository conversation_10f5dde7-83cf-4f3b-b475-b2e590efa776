from camel.agents import Chat<PERSON>gent
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from camel.messages import BaseMessage

# If you encounter openai.InternalServerError: Error code: 502, you can try setting http_proxy and https_proxy
# import os

# os.environ["http_proxy"] = "http://localhost:11434/v1/"
# os.environ["https_proxy"] = "http://localhost:11434/v1/"

model = ModelFactory.create(
    model_platform=ModelPlatformType.OLLAMA,
    model_type="qwen2.5:7b",  # Use the model name displayed in 'ollama list'
    model_config_dict={
        "temperature": 0.4,
    },
    url="http://localhost:11434/v1/"
)

agent = ChatAgent(
    model=model,
    output_language='中文'
)

print("与 CAMEL 代理开始对话 (输入 '退出' 结束):")

while True:
    user_input = input("你: ")
    if user_input.lower() == '退出':
        print("对话结束。")
        break

    # Create a BaseMessage from user input
    user_msg = BaseMessage.make_user_message(
        role_name="User",
        content=user_input
    )

    # Get model response
    response = agent.step(user_msg)
    if response and response.msgs:
        print(f"代理: {response.msgs[0].content}")
    else:
        print("代理: 未收到有效回复。")
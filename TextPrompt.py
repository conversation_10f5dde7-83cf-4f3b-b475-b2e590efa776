#提示词书写模板：

from camel.prompts import TextPrompt
prompt = TextPrompt('Please enter your name and age: {name}, {age}')
print(prompt)  
#>>> 'Please enter your name and age: {name}, {age}'

from camel.prompts import TextPrompt
prompt = TextPrompt('Please enter your name and age: {name}, {age}')
print(prompt.key_words)
#>>> {'name', 'age'}

#format 方法重写数据
from camel.prompts import TextPrompt
prompt = TextPrompt('Your name and age are: {name}, {age}')
name, age = '<PERSON>', 30
formatted_prompt = prompt.format(name=name, age=age)
print(formatted_prompt)  
#>>> "Your name and age are: John, 30"

#只提供部分值进行部分格式化
from camel.prompts import TextPrompt
prompt = TextPrompt('Your name and age are: {name}, {age}')
name = 'John'
partial_formatted_prompt = prompt.format(name=name)
print(partial_formatted_prompt)  
#>>> "Your name and age are: <PERSON>, {age}"


"""
简化的BM25修复测试脚本

该脚本专门用于测试BM25初始化问题的修复效果，
使用最小化的测试场景来快速验证修复是否有效。
"""

import os
from dotenv import load_dotenv

def test_bm25_simple():
    """简化的BM25测试"""
    
    print("=" * 60)
    print("简化BM25修复测试")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("❌ 错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return False
    
    try:
        # 导入修复后的系统
        print("🔧 导入增强版RAG系统...")
        from test_RAG_Enhanced import EnhancedRAGSystem
        
        # 初始化系统
        print("🚀 初始化系统...")
        rag_system = EnhancedRAGSystem(api_key)
        
        # 检查初始状态
        print("\n📊 检查初始检索器状态:")
        rag_system.check_retrievers_status()
        
        # 设置评估知识库（较小的文档，更容易成功）
        print("\n📚 设置评估知识库...")
        rag_system.setup_evaluation_knowledge_base(chunk_size=100)
        
        # 检查设置后状态
        print("\n📊 检查设置后检索器状态:")
        status = rag_system.check_retrievers_status()
        
        # 测试基本检索功能
        print("\n🔍 测试基本检索功能...")
        test_query = "什么是CAMEL AI？"
        
        # 测试向量检索（应该总是工作）
        print("测试向量检索...")
        try:
            vector_results = rag_system.vector_retriever.query(query=test_query, top_k=3)
            print(f"✅ 向量检索成功，返回 {len(vector_results)} 个结果")
        except Exception as e:
            print(f"❌ 向量检索失败: {e}")
            return False
        
        # 测试BM25检索（可能失败，但不应该导致系统崩溃）
        if status.get('bm25_retriever', False):
            print("测试BM25检索...")
            try:
                bm25_results = rag_system.bm25_retriever.query(query=test_query, top_k=3)
                print(f"✅ BM25检索成功，返回 {len(bm25_results)} 个结果")
            except Exception as e:
                print(f"⚠️ BM25检索失败: {e}")
        else:
            print("⚠️ BM25检索器不可用，跳过测试")
        
        # 测试RRF检索（关键测试，之前会崩溃的地方）
        print("\n🎯 测试RRF检索（关键测试）...")
        try:
            rrf_results = rag_system.retrieve_with_rrf(
                query=test_query,
                top_k=5,
                rrf_k=3
            )
            print(f"✅ RRF检索成功！返回 {len(rrf_results)} 个结果")
            
            # 显示结果来源
            sources = [result.get('source', 'Unknown') for result in rrf_results]
            print(f"结果来源: {set(sources)}")
            
        except Exception as e:
            print(f"❌ RRF检索失败: {e}")
            return False
        
        # 测试完整的enhanced_query
        print("\n🚀 测试完整的enhanced_query...")
        try:
            enhanced_results = rag_system.enhanced_query(
                original_query=test_query,
                use_rewriting=False,  # 简化测试，不使用重写
                use_hyde=False,       # 简化测试，不使用HyDE
                use_rrf=True,         # 重点测试RRF
                top_k=5,
                rrf_k=3
            )
            
            print(f"✅ 完整增强查询成功！")
            print(f"检索方法: {enhanced_results.get('retrieval_method', 'Unknown')}")
            print(f"返回文档数: {len(enhanced_results.get('retrieved_docs', []))}")
            
        except Exception as e:
            print(f"❌ 完整增强查询失败: {e}")
            return False
        
        # 测试总结
        print("\n" + "=" * 60)
        print("✅ 简化测试通过！")
        print("=" * 60)
        
        print("修复验证结果:")
        print("  ✅ 系统初始化正常")
        print("  ✅ 知识库设置成功")
        print("  ✅ 向量检索工作正常")
        if status.get('bm25_retriever', False):
            print("  ✅ BM25检索器可用")
        else:
            print("  ⚠️ BM25检索器不可用（但系统正常运行）")
        print("  ✅ RRF检索不再崩溃")
        print("  ✅ 完整增强查询功能正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nltk_data():
    """测试NLTK数据是否正确下载"""
    
    print("\n" + "=" * 60)
    print("NLTK数据测试")
    print("=" * 60)
    
    try:
        import nltk
        
        # 检查punkt
        try:
            nltk.data.find('tokenizers/punkt')
            print("✅ NLTK punkt数据包可用")
        except LookupError:
            print("❌ NLTK punkt数据包不可用")
            return False
        
        # 检查stopwords
        try:
            nltk.data.find('corpora/stopwords')
            print("✅ NLTK stopwords数据包可用")
        except LookupError:
            print("❌ NLTK stopwords数据包不可用")
            return False
        
        # 测试基本功能
        from nltk.tokenize import word_tokenize
        from nltk.corpus import stopwords
        
        test_text = "This is a test sentence for NLTK."
        tokens = word_tokenize(test_text)
        stop_words = set(stopwords.words('english'))
        
        print(f"✅ 分词测试成功: {tokens}")
        print(f"✅ 停用词测试成功: 共{len(stop_words)}个英文停用词")
        
        return True
        
    except Exception as e:
        print(f"❌ NLTK测试失败: {e}")
        return False

if __name__ == "__main__":
    # 首先测试NLTK数据
    nltk_ok = test_nltk_data()
    
    if nltk_ok:
        print("\n✅ NLTK数据测试通过，继续BM25测试...")
        # 运行BM25修复测试
        success = test_bm25_simple()
        
        if success:
            print("\n🎉 所有测试通过！BM25修复有效！")
        else:
            print("\n❌ BM25测试失败，请检查错误信息")
    else:
        print("\n❌ NLTK数据测试失败，这可能是BM25问题的根源")
        print("建议手动运行以下命令:")
        print("  python -c \"import nltk; nltk.download('punkt'); nltk.download('stopwords')\"")

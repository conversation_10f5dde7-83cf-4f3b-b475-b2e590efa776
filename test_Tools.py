from camel.toolkits import SearchToolkit, MathToolkit
from camel.societies import RolePlaying
from camel.types.agents import ToolCallingRecord
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from colorama import Fore
import os
from dotenv import load_dotenv

# 环境配置
load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 创建模型
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

# 定义工具包
tools_list = [
    *SearchToolkit().get_tools(),
    *MathToolkit().get_tools()
]

# 设置任务
task_prompt = ("假设现在是2024年，牛津大学的成立年份，并计算出其当前年龄。"
               "然后再将这个年龄加上10年。使用百度搜索工具。")

# 设置角色扮演
role_play_session = RolePlaying(
    assistant_role_name="搜索者",
    user_role_name="教授",
    assistant_agent_kwargs=dict(model=model, tools=tools_list),
    user_agent_kwargs=dict(model=model),
    task_prompt=task_prompt,
    with_task_specify=False,
    output_language='中文'
)

# 初始化对话
input_msg = role_play_session.init_chat()

# 改进的对话循环 - 添加安全机制
max_turns = 10
turn_count = 0

print(Fore.CYAN + f"开始执行任务: {task_prompt}\n")
print(Fore.YELLOW + "=" * 60)

while turn_count < max_turns:
    try:
        assistant_response, user_response = role_play_session.step(input_msg)
        
        # 简化但必要的输出
        print(Fore.WHITE + f"\n[Turn {turn_count + 1}]")
        print(Fore.GREEN + f"助手: {assistant_response.msg.content}")
        print(Fore.BLUE + f"用户: {user_response.msg.content}")
        
        # 显示工具调用信息
        if assistant_response.info['tool_calls']:
            print(Fore.MAGENTA + f"工具调用: {assistant_response.info['tool_calls']}")
        
        # 检查终止条件
        if assistant_response.terminated or user_response.terminated:
            print(Fore.RED + "对话终止")
            if assistant_response.terminated:
                print(Fore.RED + f"助手终止原因: {assistant_response.info.get('termination_reasons', '未知')}")
            if user_response.terminated:
                print(Fore.RED + f"用户终止原因: {user_response.info.get('termination_reasons', '未知')}")
            break
        
        # 检查任务完成标识
        if "CAMEL_TASK_DONE" in user_response.msg.content:
            print(Fore.GREEN + "任务完成!")
            break
            
        input_msg = assistant_response.msg
        turn_count += 1
        
        print(Fore.YELLOW + "-" * 60)
        
    except Exception as e:
        print(Fore.RED + f"执行错误: {e}")
        break


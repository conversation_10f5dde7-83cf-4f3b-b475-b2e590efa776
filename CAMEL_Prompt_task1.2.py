from camel.agents import TaskSpecifyAgent
from camel.models import ModelFactory
from camel.prompts import TextPrompt, CodePromptTemplateDict
from camel.types import ModelPlatformType, TaskType
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 生成模型实例
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

# AI编程导师Agent
class AIProgrammingTutorAgent:
    def __init__(self, model):
        self.model = model
        
        # 定义AI编程导师的提示模板（CodePromptTemplateDict）
        self.tutor_prompt_templates = CodePromptTemplateDict(
            {
                "learning_plan": TextPrompt(
                    """你是一位专业的编程导师，旨在帮助用户学习新的编程语言。
                    请为学习 **{language}** 制定一个详细的学习计划。
                    学习计划应该包括以下阶段，每个阶段都提供详细的说明、核心概念和建议的学习资源：

                    1. **基础语法和数据类型**:
                       - 核心概念:
                       - 建议学习资源:

                    2. **控制流和函数**:
                       - 核心概念:
                       - 建议学习资源:

                    3. **数据结构和算法初步**:
                       - 核心概念:
                       - 建议学习资源:

                    4. **面向对象编程 (OOP) 基础**:
                       - 核心概念:
                       - 建议学习资源:

                    5. **模块、包和错误处理**:
                       - 核心概念:
                       - 建议学习资源:

                    6. **文件操作和常用库**:
                       - 核心概念:
                       - 建议学习资源:

                    请确保计划循序渐进，易于理解和实践。
                    """
                ),
                "code_example": TextPrompt(
                    """请为 **{language}** 中的 **{concept}** 提供一个清晰、简洁的代码示例。
                    代码示例应包含必要的注释，并演示该概念的实际应用。
                    """
                ),
                "exercise_problem": TextPrompt(
                    """请为学习 **{language}** 中 **{concept}** 的用户设计一个练习题目。
                    题目应包含明确的描述、输入输出要求和预期结果的示例。
                    并提供一个参考答案（可以是一个简单的函数实现）。
                    """
                )
            }
        )
        
        # 创建AI编程导师的任务Agent
        # 默认使用 "learning_plan" 模板，后续会根据方法动态切换
        self.tutor_agent = TaskSpecifyAgent(
            model=self.model,
            task_type=TaskType.AI_SOCIETY, 
            task_specify_prompt=self.tutor_prompt_templates["learning_plan"], 
            output_language='中文'
        )

    def generate_learning_plan(self, language="Python"):
        """Generates a detailed learning plan for the specified language."""
        print(f"\n为学习 {language} 生成学习计划：")
        print("=" * 60)
        
        # 动态更新 Agent 的提示，使用学习计划模板
        self.tutor_agent.task_specify_prompt = self.tutor_prompt_templates["learning_plan"]
        
        response = self.tutor_agent.run(
            task_prompt=f"生成一个关于学习 {language} 的详细计划",
            meta_dict={"language": language}
        )
        
        print(response)
        print("=" * 60)
        return response

    def generate_code_example(self, language, concept):
        """Generates a code example for a specific concept in the given language."""
        print(f"\n为 {language} 中的 {concept} 生成代码示例：")
        print("-" * 50)
        
        # 动态更新 Agent 的提示，使用练习题目模板
        self.tutor_agent.task_specify_prompt = self.tutor_prompt_templates["code_example"]
        
        response = self.tutor_agent.run(
            task_prompt=f"生成一个关于 {language} 中 {concept} 的代码示例",
            meta_dict={"language": language, "concept": concept}
        )
        
        print(response)
        print("-" * 50)
        return response

    def generate_exercise_problem(self, language, concept):
        """Generates an exercise problem with a reference answer for a specific concept."""
        print(f"\n为 {language} 中的 {concept} 设计练习题目：")
        print("-" * 50)
        
        # 动态更新 Agent 的提示，使用代码示例模板
        self.tutor_agent.task_specify_prompt = self.tutor_prompt_templates["exercise_problem"]
        
        response = self.tutor_agent.run(
            task_prompt=f"为 {language} 中 {concept} 设计一个练习题目",
            meta_dict={"language": language, "concept": concept}
        )
        
        print(response)
        print("-" * 50)
        return response

#主程序
def main():
    # AI编程导师系统
    tutor_system = AIProgrammingTutorAgent(model)
    
    print("AI 编程学习助手")
    print("帮助你逐步学习一门新的编程语言。")
    
    # 1.生成Python学习计划
    python_learning_plan = tutor_system.generate_learning_plan("Python")

    # 2.为特定概念生成代码
    code_example_variables = tutor_system.generate_code_example("Python", "变量和数据类型")

    # 3.为特定概念生成习题
    exercise_problem_functions = tutor_system.generate_exercise_problem("Python", "函数")

if __name__ == "__main__":
    main()

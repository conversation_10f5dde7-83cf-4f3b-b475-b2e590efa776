"""
增强版RAG系统：集成查询重写(Rewriting)、假设文档嵌入(HyDE)和评估功能

该脚本实现了一个增强的RAG系统，结合了以下技术：
1. 查询重写(Query Rewriting)：修正用户查询中的错别字和表达问题
2. HyDE (Hypothetical Document Embeddings)：生成假设文档来改善检索效果
3. RRF (Reciprocal Rank Fusion)：结合多种检索方法的重排序算法
4. RAG评估模块：使用准确率、召回率、F1值等指标评估检索质量

HyDE方法的核心思想：
- 使用LLM为用户查询生成假设文档
- 这些假设文档虽然可能包含错误，但与知识库中的真实文档相关联
- 通过假设文档的向量表示来检索相似的真实文档，提高检索准确性

评估方法：
- 准确率(Precision)：衡量检索结果的相关性
- 召回率(Recall)：衡量检索结果的覆盖范围
- F1值：综合考虑准确率和召回率
- 相似度评估：使用TF-IDF和余弦相似度计算预期答案与检索结果的相似度
"""

import os
import requests
import numpy as np
from typing import List, Dict, Any

# 导入CAMEL框架相关模块
from camel.models import ModelFactory
from camel.types import ModelPlatformType
from camel.agents import ChatAgent
from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever, BM25Retriever
from camel.storages.vectordb_storages import QdrantStorage

# 导入环境变量模块
from dotenv import load_dotenv

# 导入评估相关模块
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class RAGEvaluator:
    """
    RAG系统评估器

    用于评估检索模块的性能，包括准确率、召回率、F1值和相似度评估
    """

    def __init__(self, similarity_threshold: float = 0.5):
        """
        初始化评估器

        Args:
            similarity_threshold: 相似度阈值，超过此值认为检索结果是相关的
        """
        self.similarity_threshold = similarity_threshold

    def compute_similarity(self, expected: str, retrieved: str) -> float:
        """
        计算预期答案与检索结果的相似度

        Args:
            expected: 预期答案
            retrieved: 检索到的文本

        Returns:
            相似度分数 (0-1之间)
        """
        try:
            vectorizer = TfidfVectorizer()
            tfidf = vectorizer.fit_transform([expected, retrieved])
            similarity_matrix = cosine_similarity(tfidf, tfidf)
            return similarity_matrix[0, 1]
        except Exception as e:
            print(f"计算相似度时出错: {e}")
            return 0.0

    def calculate_precision(self, retrieved: List[str], relevant: List[str], threshold: float = None) -> float:
        """
        计算精确率（Precision）

        Args:
            retrieved: 检索到的文档列表
            relevant: 相关文档列表
            threshold: 相似度阈值

        Returns:
            精确率分数
        """
        if not retrieved:
            return 0.0

        threshold = threshold or self.similarity_threshold
        correct = 0

        for r in retrieved:
            for rel in relevant:
                similarity = self.compute_similarity(rel, r)
                if similarity >= threshold:
                    correct += 1
                    break

        return correct / len(retrieved)

    def calculate_recall(self, retrieved: List[str], relevant: List[str], threshold: float = None) -> float:
        """
        计算召回率（Recall）

        Args:
            retrieved: 检索到的文档列表
            relevant: 相关文档列表
            threshold: 相似度阈值

        Returns:
            召回率分数
        """
        if not relevant:
            return 0.0

        threshold = threshold or self.similarity_threshold
        correct = 0

        for rel in relevant:
            for r in retrieved:
                similarity = self.compute_similarity(rel, r)
                if similarity >= threshold:
                    correct += 1
                    break

        return correct / len(relevant)

    def calculate_f1(self, precision: float, recall: float) -> float:
        """
        计算F1值

        Args:
            precision: 精确率
            recall: 召回率

        Returns:
            F1分数
        """
        if precision + recall == 0:
            return 0.0
        return 2 * (precision * recall) / (precision + recall)

    def evaluate_single_query(self, query: str, expected_answers: List[str],
                             retrieved_texts: List[str], threshold: float = None) -> Dict[str, Any]:
        """
        评估单个查询的检索质量

        Args:
            query: 查询文本
            expected_answers: 预期答案列表
            retrieved_texts: 检索到的文本列表
            threshold: 相似度阈值

        Returns:
            评估结果字典
        """
        threshold = threshold or self.similarity_threshold

        # 计算精确率、召回率和F1值
        precision = self.calculate_precision(retrieved_texts, expected_answers, threshold)
        recall = self.calculate_recall(retrieved_texts, expected_answers, threshold)
        f1 = self.calculate_f1(precision, recall)

        # 计算平均相似度
        similarities = []
        for expected in expected_answers:
            for retrieved in retrieved_texts:
                similarities.append(self.compute_similarity(expected, retrieved))

        avg_similarity = np.mean(similarities) if similarities else 0.0

        return {
            "query": query,
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "avg_similarity": avg_similarity,
            "retrieved_texts": retrieved_texts,
            "expected_answers": expected_answers,
            "threshold": threshold
        }

    def evaluate_multiple_queries(self, test_cases: List[Dict[str, Any]],
                                 retrieval_function, threshold: float = None) -> Dict[str, Any]:
        """
        评估多个查询的检索质量

        Args:
            test_cases: 测试用例列表，每个包含'query'和'expected_answers'
            retrieval_function: 检索函数，接受query参数返回检索结果
            threshold: 相似度阈值

        Returns:
            整体评估结果
        """
        threshold = threshold or self.similarity_threshold
        evaluation_results = []

        print("开始评估多个查询...")
        print("=" * 60)

        for i, test_case in enumerate(test_cases):
            query = test_case["query"]
            expected_answers = test_case["expected_answers"]

            print(f"\n评估查询 {i+1}: {query}")
            print("-" * 40)

            # 执行检索
            try:
                retrieved_results = retrieval_function(query)
                if isinstance(retrieved_results, list) and len(retrieved_results) > 0:
                    if isinstance(retrieved_results[0], dict):
                        retrieved_texts = [result.get("text", str(result)) for result in retrieved_results]
                    else:
                        retrieved_texts = [str(result) for result in retrieved_results]
                else:
                    retrieved_texts = []
            except Exception as e:
                print(f"检索时出错: {e}")
                retrieved_texts = []

            # 评估单个查询
            evaluation = self.evaluate_single_query(query, expected_answers, retrieved_texts, threshold)
            evaluation_results.append(evaluation)

            # 打印详细结果
            print(f"预期答案: {expected_answers}")
            print(f"检索结果: {[text[:100] + '...' if len(text) > 100 else text for text in retrieved_texts]}")
            print(f"精确率: {evaluation['precision']:.4f}")
            print(f"召回率: {evaluation['recall']:.4f}")
            print(f"F1分数: {evaluation['f1']:.4f}")
            print(f"平均相似度: {evaluation['avg_similarity']:.4f}")

        # 计算整体评估结果
        total_precision = np.mean([result["precision"] for result in evaluation_results])
        total_recall = np.mean([result["recall"] for result in evaluation_results])
        total_f1 = np.mean([result["f1"] for result in evaluation_results])
        total_similarity = np.mean([result["avg_similarity"] for result in evaluation_results])

        overall_results = {
            "individual_results": evaluation_results,
            "overall_metrics": {
                "average_precision": total_precision,
                "average_recall": total_recall,
                "average_f1": total_f1,
                "average_similarity": total_similarity
            },
            "threshold": threshold,
            "num_queries": len(test_cases)
        }

        print("\n" + "=" * 60)
        print("整体评估结果:")
        print(f"平均精确率: {total_precision:.4f}")
        print(f"平均召回率: {total_recall:.4f}")
        print(f"平均F1分数: {total_f1:.4f}")
        print(f"平均相似度: {total_similarity:.4f}")
        print("=" * 60)

        return overall_results

class EnhancedRAGSystem:
    """
    增强版RAG系统类

    集成了查询重写、HyDE、ReRank(RRF算法)和传统RAG功能
    """

    def __init__(self, api_key: str):
        """
        初始化增强版RAG系统

        Args:
            api_key: ModelScope API密钥
        """
        self.api_key = api_key

        # 初始化嵌入模型
        self.embedding_model = SentenceTransformerEncoder(
            model_name='intfloat/e5-large-v2'
        )

        # 初始化向量存储
        self.vector_storage = QdrantStorage(
            vector_dim=self.embedding_model.get_output_dim(),
            collection="enhanced_rag_collection",
            path="storage_enhanced_rag",
            collection_name="增强RAG知识库"
        )

        # 初始化向量检索器
        self.vector_retriever = VectorRetriever(
            embedding_model=self.embedding_model,
            storage=self.vector_storage
        )

        # 初始化BM25检索器（用于ReRank）
        self.bm25_retriever = BM25Retriever()

        # 初始化LLM模型
        self.model = ModelFactory.create(
            model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
            model_type="Qwen/Qwen2.5-72B-Instruct",
            url='https://api-inference.modelscope.cn/v1/',
            api_key=self.api_key
        )

        # 初始化各种代理
        self._init_agents()

        # 初始化评估器
        self.evaluator = RAGEvaluator(similarity_threshold=0.3)

    def rrf(self, vector_results: List[Dict], text_results: List[Dict], k: int = 10, m: int = 60) -> List[tuple]:
        """
        使用RRF (Reciprocal Rank Fusion) 算法对两组检索结果进行重排序

        RRF算法通过结合不同检索方法的排名来提高检索效果。
        公式：RRF_score = Σ(1/(rank + m))，其中m是超参数

        Args:
            vector_results: 向量召回的结果列表，每个元素是包含'text'的字典
            text_results: 文本召回的结果列表，每个元素是包含'text'的字典
            k: 排序后返回前k个结果
            m: RRF算法的超参数，用于平滑排名分数

        Returns:
            重排序后的结果列表，每个元素是(文档内容, 融合分数)的元组
        """
        doc_scores = {}

        # 处理向量检索结果
        # 为每个文档分配基于排名的分数：1/(rank + m)
        for rank, result in enumerate(vector_results):
            text = result['text']
            doc_scores[text] = doc_scores.get(text, 0) + 1 / (rank + m)

        # 处理文本检索结果
        # 累加来自不同检索方法的分数
        for rank, result in enumerate(text_results):
            text = result['text']
            doc_scores[text] = doc_scores.get(text, 0) + 1 / (rank + m)

        # 按融合分数降序排序并返回前k个结果
        sorted_results = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:k]

        print(f"RRF融合完成，共处理 {len(doc_scores)} 个唯一文档，返回前 {len(sorted_results)} 个结果")

        return sorted_results

    def _init_agents(self):
        """初始化不同功能的AI代理"""
        
        # 查询重写代理
        rewriting_sys_msg = """
        你是RAG模块中的Rewriting助手，目的是理解用户的提问，并且重新组织和优化用户的提问表达，
        修正用户输入中可能存在的错别字的情况并重构提问来使得句子表达更加通顺严谨。
        请直接输出重写后的查询，不要添加额外的解释。
        """
        self.rewriting_agent = ChatAgent(
            system_message=rewriting_sys_msg,
            model=self.model
        )
        
        # HyDE假设文档生成代理
        hyde_sys_msg = """
        你是一个专门生成假设文档的助手。根据用户的查询，生成一个相关的假设文档片段。
        这个文档应该：
        1. 直接回答用户的问题
        2. 包含相关的技术细节和概念
        3. 使用专业但清晰的语言
        4. 长度适中（200-400字）
        
        请直接输出假设文档内容，不要添加额外的解释或格式。
        """
        self.hyde_agent = ChatAgent(
            system_message=hyde_sys_msg,
            model=self.model
        )
        
        # RAG回答生成代理
        rag_sys_msg = """
        你是一个帮助回答问题的助手。
        我会给你原始查询和检索到的上下文信息。
        请根据检索到的上下文回答原始查询。
        如果上下文信息不足以回答问题，请说"根据提供的信息，我无法完全回答这个问题"。
        请确保回答准确、完整且有条理。
        """
        self.rag_agent = ChatAgent(
            system_message=rag_sys_msg,
            model=self.model
        )
    
    def setup_knowledge_base(self, pdf_url: str = None, pdf_path: str = None):
        """
        设置知识库，同时初始化向量检索器和BM25检索器

        Args:
            pdf_url: PDF文件的URL
            pdf_path: 本地PDF文件路径
        """
        # 创建本地数据目录
        os.makedirs('local_data', exist_ok=True)

        if pdf_url:
            # 下载PDF文件
            print("正在下载PDF文件...")
            response = requests.get(pdf_url)
            pdf_path = 'local_data/knowledge_base.pdf'
            with open(pdf_path, 'wb') as file:
                file.write(response.content)
            print(f"PDF文件已下载到: {pdf_path}")

        if pdf_path:
            # 处理PDF文件并建立向量数据库
            print("正在处理PDF文件并建立向量数据库...")
            self.vector_retriever.process(content=pdf_path)

            # 同时为BM25检索器处理文档
            print("正在为BM25检索器处理文档...")
            self.bm25_retriever.process(content_input_path=pdf_path)

            print("知识库设置完成！（向量检索器 + BM25检索器）")
    
    def rewrite_query(self, original_query: str) -> str:
        """
        重写用户查询
        
        Args:
            original_query: 原始用户查询
            
        Returns:
            重写后的查询
        """
        rewrite_prompt = f"用户的原始提问如下：{original_query}"
        response = self.rewriting_agent.step(rewrite_prompt)
        rewritten_query = response.msgs[0].content.strip()
        
        print(f"原始查询: {original_query}")
        print(f"重写查询: {rewritten_query}")
        
        return rewritten_query
    
    def generate_hypothetical_document(self, query: str) -> str:
        """
        生成假设文档 (HyDE方法)
        
        Args:
            query: 用户查询
            
        Returns:
            生成的假设文档
        """
        hyde_prompt = f"请为以下查询生成一个相关的假设文档：{query}"
        response = self.hyde_agent.step(hyde_prompt)
        hypothetical_doc = response.msgs[0].content.strip()
        
        print(f"生成的假设文档:\n{hypothetical_doc}")
        
        return hypothetical_doc

    def retrieve_with_rrf(self, query: str, top_k: int = 10, rrf_k: int = 5, m: int = 60) -> List[Dict[str, Any]]:
        """
        使用RRF算法结合向量检索和BM25检索的结果

        Args:
            query: 用户查询
            top_k: 每个检索器返回的文档数量
            rrf_k: RRF融合后返回的文档数量
            m: RRF算法的超参数

        Returns:
            RRF融合后的检索结果列表
        """
        print(f"使用RRF算法进行混合检索...")

        # 向量检索
        print("执行向量检索...")
        vector_results = self.vector_retriever.query(query=query, top_k=top_k)

        # BM25检索
        print("执行BM25检索...")
        bm25_results = self.bm25_retriever.query(query=query, top_k=top_k)

        # 使用RRF算法融合结果
        print("使用RRF算法融合检索结果...")
        rrf_results = self.rrf(vector_results, bm25_results, k=rrf_k, m=m)

        # 转换RRF结果格式以保持与其他检索方法的一致性
        formatted_results = []
        for text, score in rrf_results:
            formatted_results.append({
                'text': text,
                'rrf_score': score,
                'source': 'RRF_fusion'
            })

        print(f"RRF检索完成，返回 {len(formatted_results)} 个文档")
        for i, result in enumerate(formatted_results):
            print(f"文档 {i+1} (RRF分数: {result['rrf_score']:.4f}):")
            print(f"{result['text'][:200]}...\n")

        return formatted_results

    def retrieve_with_hyde_and_rrf(self, query: str, top_k: int = 10, rrf_k: int = 5, m: int = 60) -> List[Dict[str, Any]]:
        """
        结合HyDE和RRF的高级检索方法

        Args:
            query: 用户查询
            top_k: 每个检索器返回的文档数量
            rrf_k: RRF融合后返回的文档数量
            m: RRF算法的超参数

        Returns:
            HyDE+RRF融合后的检索结果列表
        """
        print(f"使用HyDE+RRF组合方法进行检索...")

        # 生成假设文档
        hypothetical_doc = self.generate_hypothetical_document(query)

        # 使用假设文档进行向量检索
        print("使用假设文档进行向量检索...")
        hyde_vector_results = self.vector_retriever.query(query=hypothetical_doc, top_k=top_k)

        # 使用原始查询进行BM25检索
        print("使用原始查询进行BM25检索...")
        bm25_results = self.bm25_retriever.query(query=query, top_k=top_k)

        # 使用RRF算法融合HyDE向量检索和BM25检索的结果
        print("使用RRF算法融合HyDE和BM25检索结果...")
        rrf_results = self.rrf(hyde_vector_results, bm25_results, k=rrf_k, m=m)

        # 转换结果格式
        formatted_results = []
        for text, score in rrf_results:
            formatted_results.append({
                'text': text,
                'rrf_score': score,
                'source': 'HyDE_RRF_fusion'
            })

        print(f"HyDE+RRF检索完成，返回 {len(formatted_results)} 个文档")
        for i, result in enumerate(formatted_results):
            print(f"文档 {i+1} (RRF分数: {result['rrf_score']:.4f}):")
            print(f"{result['text'][:200]}...\n")

        return formatted_results

    def retrieve_with_hyde(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        使用HyDE方法进行检索
        
        Args:
            query: 用户查询
            top_k: 返回的文档数量
            
        Returns:
            检索到的文档列表
        """
        # 生成假设文档
        hypothetical_doc = self.generate_hypothetical_document(query)
        
        # 使用假设文档进行检索
        hyde_results = self.vector_retriever.query(
            query=hypothetical_doc,
            top_k=top_k
        )
        
        # 同时使用原始查询进行检索作为对比
        original_results = self.vector_retriever.query(
            query=query,
            top_k=top_k
        )
        
        print(f"\n=== HyDE检索结果 ===")
        for i, result in enumerate(hyde_results):
            print(f"文档 {i+1} (相似度: {result.get('similarity', 'N/A')}):")
            print(f"{result['text'][:200]}...\n")
        
        print(f"=== 原始查询检索结果 ===")
        for i, result in enumerate(original_results):
            print(f"文档 {i+1} (相似度: {result.get('similarity', 'N/A')}):")
            print(f"{result['text'][:200]}...\n")
        
        return hyde_results, original_results
    
    def generate_answer(self, query: str, retrieved_docs: List[Dict[str, Any]]) -> str:
        """
        基于检索到的文档生成答案
        
        Args:
            query: 用户查询
            retrieved_docs: 检索到的文档列表
            
        Returns:
            生成的答案
        """
        # 构建上下文
        context = "\n\n".join([doc['text'] for doc in retrieved_docs])
        
        # 构建提示
        prompt = f"""
        原始查询: {query}
        
        检索到的上下文信息:
        {context}
        
        请根据上述上下文信息回答原始查询。
        """
        
        response = self.rag_agent.step(prompt)
        answer = response.msgs[0].content.strip()
        
        return answer
    
    def enhanced_query(self, original_query: str, use_rewriting: bool = True,
                      use_hyde: bool = True, use_rrf: bool = True, top_k: int = 10, rrf_k: int = 5) -> Dict[str, Any]:
        """
        执行增强版RAG查询

        Args:
            original_query: 原始用户查询
            use_rewriting: 是否使用查询重写
            use_hyde: 是否使用HyDE方法
            use_rrf: 是否使用RRF重排序算法
            top_k: 每个检索器返回的文档数量
            rrf_k: RRF融合后返回的文档数量

        Returns:
            包含各步骤结果的字典
        """
        print("=" * 60)
        print("开始执行增强版RAG查询")
        print("=" * 60)
        
        results = {
            'original_query': original_query,
            'rewritten_query': None,
            'hypothetical_document': None,
            'retrieved_docs': None,
            'final_answer': None
        }
        
        # 步骤1: 查询重写（可选）
        if use_rewriting:
            print("\n步骤1: 查询重写")
            print("-" * 30)
            rewritten_query = self.rewrite_query(original_query)
            results['rewritten_query'] = rewritten_query
            query_to_use = rewritten_query
        else:
            query_to_use = original_query
        
        # 步骤2: 文档检索
        print(f"\n步骤2: 文档检索")
        print("-" * 30)

        if use_rrf and use_hyde:
            print("使用HyDE+RRF组合方法进行检索...")
            retrieved_docs = self.retrieve_with_hyde_and_rrf(query_to_use, top_k=top_k, rrf_k=rrf_k)
            results['retrieved_docs'] = retrieved_docs
            results['retrieval_method'] = 'HyDE+RRF'
        elif use_rrf:
            print("使用RRF方法进行检索...")
            retrieved_docs = self.retrieve_with_rrf(query_to_use, top_k=top_k, rrf_k=rrf_k)
            results['retrieved_docs'] = retrieved_docs
            results['retrieval_method'] = 'RRF'
        elif use_hyde:
            print("使用HyDE方法进行检索...")
            hyde_results, original_results = self.retrieve_with_hyde(query_to_use, top_k)
            results['retrieved_docs'] = hyde_results
            results['original_retrieval_docs'] = original_results
            results['retrieval_method'] = 'HyDE'
            retrieved_docs = hyde_results
        else:
            print("使用传统向量检索方法...")
            retrieved_docs = self.vector_retriever.query(query=query_to_use, top_k=top_k)
            results['retrieved_docs'] = retrieved_docs
            results['retrieval_method'] = 'Traditional'
        
        # 步骤3: 答案生成
        print(f"\n步骤3: 答案生成")
        print("-" * 30)
        final_answer = self.generate_answer(query_to_use, retrieved_docs)
        results['final_answer'] = final_answer
        
        print(f"\n最终答案:\n{final_answer}")
        
        return results

    def create_sample_document(self) -> str:
        """
        创建示例文档用于评估测试

        Returns:
            示例文档的文件路径
        """
        sample_content = """# CAMEL AI 介绍

CAMEL AI 是一个开源的、社区驱动的AI框架，旨在简化AI应用的开发和部署。该框架提供了多种功能模块，包括数据加载、特征工程、模型训练和部署等。

## 主要特点

### 模块化设计
用户可以根据需求选择性地加载不同的功能模块。

### 易用性
提供了简单易用的API接口，降低了开发门槛。

### 扩展性
支持多种模型和后端服务，方便用户根据需求进行扩展。

## 常见问题

### 如何开始使用CAMEL AI？

首先安装框架：`pip install camel-ai`

然后引入必要的模块：`from camel import *`

参考官方文档：CAMEL AI官方文档

### CAMEL AI支持哪些功能？

CAMEL AI支持以下主要功能：
- 多智能体协作系统
- 角色扮演对话
- 任务分解和执行
- 代码生成和调试
- 文档处理和检索

### 如何进行多智能体协作？

CAMEL AI提供了完整的多智能体协作框架，包括：
1. 智能体角色定义
2. 任务分配机制
3. 通信协议
4. 协作策略

### 角色扮演功能如何实现？

角色扮演功能通过以下方式实现：
- 定义角色特征和行为模式
- 设置对话上下文和约束
- 实现角色间的交互逻辑
- 维护角色状态和记忆

## 技术架构

CAMEL AI采用模块化架构设计，主要包括：
- 核心引擎：负责任务调度和执行
- 智能体模块：实现各种AI智能体
- 通信模块：处理智能体间的通信
- 存储模块：管理数据和状态
- 接口模块：提供API和用户界面

## 应用场景

CAMEL AI适用于以下场景：
- 自动化软件开发
- 智能客服系统
- 教育培训平台
- 研究和实验
- 企业级AI应用

## 社区和支持

CAMEL AI拥有活跃的开源社区，提供：
- 详细的文档和教程
- 示例代码和最佳实践
- 技术支持和问题解答
- 定期更新和功能增强
"""

        # 确保目录存在
        os.makedirs('local_data', exist_ok=True)

        # 写入示例文档
        sample_path = 'local_data/camel_ai_sample.md'
        with open(sample_path, 'w', encoding='utf-8') as f:
            f.write(sample_content)

        print(f"示例文档已创建: {sample_path}")
        return sample_path

    def get_evaluation_test_cases(self) -> List[Dict[str, Any]]:
        """
        获取评估测试用例

        Returns:
            测试用例列表
        """
        test_cases = [
            {
                "query": "什么是CAMEL AI？",
                "expected_answers": ["CAMEL AI 是一个开源的、社区驱动的AI框架。"]
            },
            {
                "query": "如何开始使用CAMEL AI？",
                "expected_answers": ["首先安装框架：`pip install camel-ai`，然后引入必要的模块。"]
            },
            {
                "query": "CAMEL AI 的主要特点是什么？",
                "expected_answers": ["模块化设计、易用性和扩展性。"]
            },
            {
                "query": "CAMEL AI支持哪些功能？",
                "expected_answers": ["多智能体协作系统、角色扮演对话、任务分解和执行、代码生成和调试、文档处理和检索。"]
            },
            {
                "query": "角色扮演功能如何实现？",
                "expected_answers": ["通过定义角色特征和行为模式、设置对话上下文和约束、实现角色间的交互逻辑、维护角色状态和记忆来实现。"]
            }
        ]
        return test_cases

    def evaluate_retrieval_methods(self, test_cases: List[Dict[str, Any]] = None,
                                  similarity_threshold: float = 0.5) -> Dict[str, Any]:
        """
        评估不同检索方法的性能

        Args:
            test_cases: 测试用例列表
            similarity_threshold: 相似度阈值

        Returns:
            各种检索方法的评估结果
        """
        if test_cases is None:
            test_cases = self.get_evaluation_test_cases()

        # 更新评估器的阈值
        self.evaluator.similarity_threshold = similarity_threshold

        # 定义不同的检索方法
        retrieval_methods = {
            "传统向量检索": lambda query: self.vector_retriever.query(query=query, top_k=3),
            "HyDE检索": lambda query: self.retrieve_with_hyde(query, top_k=3)[0],  # 只取HyDE结果
            "RRF检索": lambda query: self.retrieve_with_rrf(query, top_k=10, rrf_k=3),
            "HyDE+RRF检索": lambda query: self.retrieve_with_hyde_and_rrf(query, top_k=10, rrf_k=3)
        }

        evaluation_results = {}

        print("=" * 80)
        print("开始评估不同检索方法的性能")
        print("=" * 80)

        for method_name, retrieval_func in retrieval_methods.items():
            print(f"\n{'='*20} 评估 {method_name} {'='*20}")

            try:
                # 评估当前检索方法
                results = self.evaluator.evaluate_multiple_queries(
                    test_cases, retrieval_func, similarity_threshold
                )
                evaluation_results[method_name] = results

            except Exception as e:
                print(f"评估 {method_name} 时出错: {e}")
                evaluation_results[method_name] = {
                    "error": str(e),
                    "overall_metrics": {
                        "average_precision": 0.0,
                        "average_recall": 0.0,
                        "average_f1": 0.0,
                        "average_similarity": 0.0
                    }
                }

        # 生成对比报告
        self._generate_comparison_report(evaluation_results)

        return evaluation_results

    def _generate_comparison_report(self, evaluation_results: Dict[str, Any]):
        """
        生成检索方法对比报告

        Args:
            evaluation_results: 各种方法的评估结果
        """
        print("\n" + "=" * 80)
        print("检索方法性能对比报告")
        print("=" * 80)

        # 表头
        print(f"{'方法名称':<20} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'相似度':<10}")
        print("-" * 70)

        # 收集所有方法的指标用于排序
        method_scores = []

        for method_name, results in evaluation_results.items():
            if "error" in results:
                print(f"{method_name:<20} {'错误':<10} {'错误':<10} {'错误':<10} {'错误':<10}")
                continue

            metrics = results["overall_metrics"]
            precision = metrics["average_precision"]
            recall = metrics["average_recall"]
            f1 = metrics["average_f1"]
            similarity = metrics["average_similarity"]

            print(f"{method_name:<20} {precision:<10.4f} {recall:<10.4f} {f1:<10.4f} {similarity:<10.4f}")

            method_scores.append({
                "method": method_name,
                "f1": f1,
                "precision": precision,
                "recall": recall,
                "similarity": similarity
            })

        # 按F1分数排序并显示最佳方法
        if method_scores:
            best_method = max(method_scores, key=lambda x: x["f1"])
            print("\n" + "=" * 80)
            print(f"最佳检索方法: {best_method['method']}")
            print(f"F1分数: {best_method['f1']:.4f}")
            print(f"精确率: {best_method['precision']:.4f}")
            print(f"召回率: {best_method['recall']:.4f}")
            print(f"相似度: {best_method['similarity']:.4f}")
            print("=" * 80)


def main():
    """主函数：演示增强版RAG系统的使用和评估"""

    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

    if not api_key:
        print("错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return

    # 初始化增强版RAG系统
    print("初始化增强版RAG系统...")
    rag_system = EnhancedRAGSystem(api_key)

    # 选择知识库设置方式
    print("\n选择知识库设置方式:")
    print("1. 使用CAMEL论文 (在线下载)")
    print("2. 使用示例文档 (本地创建)")

    choice = input("请选择 (1 或 2，默认为2): ").strip()

    if choice == "1":
        # 设置知识库（使用CAMEL论文）
        print("使用CAMEL论文作为知识库...")
        pdf_url = "https://arxiv.org/pdf/2303.17760.pdf"
        rag_system.setup_knowledge_base(pdf_url=pdf_url)
    else:
        # 使用示例文档
        print("创建并使用示例文档作为知识库...")
        sample_path = rag_system.create_sample_document()
        rag_system.setup_knowledge_base(pdf_path=sample_path)
    
    # 选择运行模式
    print("\n选择运行模式:")
    print("1. 演示模式 - 展示不同检索方法的效果")
    print("2. 评估模式 - 评估检索方法的性能指标")
    print("3. 完整模式 - 演示 + 评估")

    mode = input("请选择模式 (1, 2, 或 3，默认为3): ").strip()

    if mode in ["1", "3"]:
        # 演示模式
        print("\n" + "=" * 80)
        print("演示模式：展示不同检索方法的效果")
        print("=" * 80)

        # 测试查询（包含错别字）
        test_queries = [
            "我盖如何解决CAMEL中文档冲服的问题问题呢，几个版本的文档可能存在代码结构的冲突",
            "CAMEL是什么东东？它有什么特点吗",
            "角色扮演在CAMEL中是怎么实现的"
        ]

        # 测试不同的检索方法组合
        test_methods = [
            {"name": "传统RAG", "rewriting": False, "hyde": False, "rrf": False},
            {"name": "查询重写+传统RAG", "rewriting": True, "hyde": False, "rrf": False},
            {"name": "HyDE增强RAG", "rewriting": False, "hyde": True, "rrf": False},
            {"name": "RRF重排序RAG", "rewriting": False, "hyde": False, "rrf": True},
            {"name": "完整增强RAG (Rewriting+HyDE+RRF)", "rewriting": True, "hyde": True, "rrf": True}
        ]

        for i, query in enumerate(test_queries):
            print("\n" + "=" * 100)
            print(f"测试查询 {i+1}: {query}")
            print("=" * 100)

            # 只对第一个查询测试所有方法，其他查询使用完整增强方法
            methods_to_test = test_methods if i == 0 else [test_methods[-1]]

            for method in methods_to_test:
                print(f"\n{'='*20} {method['name']} {'='*20}")

                # 执行增强版RAG查询
                results = rag_system.enhanced_query(
                    original_query=query,
                    use_rewriting=method['rewriting'],
                    use_hyde=method['hyde'],
                    use_rrf=method['rrf'],
                    top_k=10,
                    rrf_k=3
                )

                print(f"检索方法: {results.get('retrieval_method', 'Unknown')}")
                print("-" * 50)

            print("\n" + "=" * 100)

    if mode in ["2", "3"]:
        # 评估模式
        print("\n" + "=" * 80)
        print("评估模式：评估检索方法的性能指标")
        print("=" * 80)

        # 设置相似度阈值
        threshold_input = input("请输入相似度阈值 (0-1之间，默认0.5): ").strip()
        try:
            similarity_threshold = float(threshold_input) if threshold_input else 0.5
            similarity_threshold = max(0.0, min(1.0, similarity_threshold))  # 限制在0-1之间
        except ValueError:
            similarity_threshold = 0.5

        print(f"使用相似度阈值: {similarity_threshold}")

        # 执行评估
        try:
            evaluation_results = rag_system.evaluate_retrieval_methods(
                similarity_threshold=similarity_threshold
            )

            # 保存评估结果
            import json
            results_file = 'local_data/evaluation_results.json'
            with open(results_file, 'w', encoding='utf-8') as f:
                # 转换numpy类型为Python原生类型以便JSON序列化
                def convert_numpy(obj):
                    if isinstance(obj, np.ndarray):
                        return obj.tolist()
                    elif isinstance(obj, np.float64):
                        return float(obj)
                    elif isinstance(obj, np.int64):
                        return int(obj)
                    return obj

                # 递归转换所有numpy类型
                def recursive_convert(data):
                    if isinstance(data, dict):
                        return {k: recursive_convert(v) for k, v in data.items()}
                    elif isinstance(data, list):
                        return [recursive_convert(item) for item in data]
                    else:
                        return convert_numpy(data)

                converted_results = recursive_convert(evaluation_results)
                json.dump(converted_results, f, ensure_ascii=False, indent=2)

            print(f"\n评估结果已保存到: {results_file}")

        except Exception as e:
            print(f"评估过程中出错: {e}")
            import traceback
            traceback.print_exc()

    print("\n" + "=" * 80)
    print("程序执行完成！")
    print("=" * 80)


if __name__ == "__main__":
    main()

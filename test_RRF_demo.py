"""
RRF (Reciprocal Rank Fusion) 算法演示

这个脚本演示了如何使用RRF算法结合向量检索和BM25检索的结果，
基于您提供的参考代码结构实现。

RRF算法的核心思想：
- 结合不同检索方法的排名来提高检索效果
- 公式：RRF_score = Σ(1/(rank + m))，其中m是超参数
- 通过融合多种检索方法的优势来获得更好的检索结果
"""

import os
import requests
from typing import List, Dict

# 导入CAMEL框架相关模块
from camel.embeddings import SentenceTransformerEncoder
from camel.retrievers import VectorRetriever, BM25Retriever
from camel.storages.vectordb_storages import QdrantStorage
from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType

from dotenv import load_dotenv

def rrf(vector_results: List[Dict], text_results: List[Dict], k: int = 10, m: int = 60):
    """
    使用RRF算法对两组检索结果进行重排序
    
    Args:
        vector_results: 向量召回的结果列表，每个元素是包含'text'的字典
        text_results: 文本召回的结果列表，每个元素是包含'text'的字典
        k: 排序后返回前k个
        m: 超参数
    
    Returns:
        重排序后的结果列表，每个元素是(文档内容, 融合分数)
    """
    doc_scores = {}
    
    print(f"处理向量检索结果: {len(vector_results)} 个文档")
    # 遍历向量检索结果
    for rank, result in enumerate(vector_results):
        text = result['text']
        score = 1 / (rank + m)
        doc_scores[text] = doc_scores.get(text, 0) + score
        print(f"  向量检索 排名{rank+1}: 分数={score:.4f}")
    
    print(f"\n处理BM25检索结果: {len(text_results)} 个文档")
    # 遍历文本检索结果
    for rank, result in enumerate(text_results):
        text = result['text']
        score = 1 / (rank + m)
        doc_scores[text] = doc_scores.get(text, 0) + score
        print(f"  BM25检索 排名{rank+1}: 分数={score:.4f}")
    
    # 按融合分数排序并返回前k个结果
    sorted_results = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:k]
    
    print(f"\nRRF融合结果 (前{k}个):")
    for i, (text, score) in enumerate(sorted_results):
        print(f"  融合排名{i+1}: 分数={score:.4f}, 文档={text[:100]}...")
    
    return sorted_results

def setup_retrievers():
    """设置检索器"""
    print("设置检索器...")
    
    # 确保数据目录存在
    os.makedirs('local_data', exist_ok=True)
    
    # 下载CAMEL论文（如果不存在）
    pdf_path = "local_data/camel_paper.pdf"
    if not os.path.exists(pdf_path):
        print("下载CAMEL论文...")
        url = "https://arxiv.org/pdf/2303.17760.pdf"
        response = requests.get(url)
        with open(pdf_path, 'wb') as file:
            file.write(response.content)
        print("论文下载完成！")
    
    # 初始化嵌入模型
    print("初始化嵌入模型...")
    embedding_model = SentenceTransformerEncoder(model_name='intfloat/e5-large-v2')
    
    # 初始化向量存储
    print("初始化向量存储...")
    vector_storage = QdrantStorage(
        vector_dim=embedding_model.get_output_dim(),
        collection="rrf_demo_collection",
        path="storage_rrf_demo",
        collection_name="RRF演示"
    )
    
    # 初始化检索器
    print("初始化向量检索器...")
    vr = VectorRetriever(embedding_model=embedding_model, storage=vector_storage)
    
    print("初始化BM25检索器...")
    bm25r = BM25Retriever()
    
    # 处理文档
    print("处理文档并建立索引...")
    vr.process(content=pdf_path)
    bm25r.process(content_input_path=pdf_path)
    
    print("检索器设置完成！")
    return vr, bm25r

def demo_rrf_retrieval():
    """演示RRF检索功能"""
    print("=" * 60)
    print("RRF (Reciprocal Rank Fusion) 算法演示")
    print("=" * 60)
    
    # 设置检索器
    vr, bm25r = setup_retrievers()
    
    # 测试查询
    test_queries = [
        "CAMEL是什么",
        "角色扮演在CAMEL中如何实现",
        "多智能体系统的协作机制"
    ]
    
    for i, query in enumerate(test_queries):
        print(f"\n{'='*80}")
        print(f"测试查询 {i+1}: {query}")
        print('='*80)
        
        # 执行向量检索
        print(f"\n--- 向量检索结果 ---")
        vector_results = vr.query(query=query, top_k=5)
        print(f"向量检索返回 {len(vector_results)} 个结果:")
        for j, result in enumerate(vector_results):
            print(f"  {j+1}. {result['text'][:150]}...")
        
        # 执行BM25检索
        print(f"\n--- BM25检索结果 ---")
        bm25_results = bm25r.query(query=query, top_k=5)
        print(f"BM25检索返回 {len(bm25_results)} 个结果:")
        for j, result in enumerate(bm25_results):
            print(f"  {j+1}. {result['text'][:150]}...")
        
        # 使用RRF融合
        print(f"\n--- RRF融合过程 ---")
        rrf_results = rrf(vector_results, bm25_results, k=3, m=60)
        
        print(f"\n--- 最终RRF结果 ---")
        print(f"RRF融合后的前3个结果:")
        for j, (text, score) in enumerate(rrf_results):
            print(f"  {j+1}. [分数: {score:.4f}] {text[:200]}...")
        
        print("\n" + "="*80)

def demo_with_llm_generation():
    """演示RRF结合LLM生成答案"""
    print("\n" + "=" * 60)
    print("RRF + LLM 生成演示")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("警告：未找到MODELSCOPE_SDK_TOKEN，跳过LLM生成演示")
        return
    
    # 设置检索器
    vr, bm25r = setup_retrievers()
    
    # 初始化LLM
    print("初始化LLM模型...")
    model = ModelFactory.create(
        model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
        model_type="Qwen/Qwen2.5-72B-Instruct",
        url='https://api-inference.modelscope.cn/v1/',
        api_key=api_key
    )
    
    # 创建RAG代理
    rag_sys_msg = """
    你是一个帮助回答问题的助手。
    我会给你用户查询和通过RRF算法检索到的相关文档。
    请根据这些文档回答用户的问题。
    如果文档信息不足以回答问题，请说明这一点。
    """
    rag_agent = ChatAgent(system_message=rag_sys_msg, model=model)
    
    # 测试查询
    query = "CAMEL框架的主要特点是什么？"
    print(f"\n用户查询: {query}")
    
    # 执行检索
    print("\n执行RRF检索...")
    vector_results = vr.query(query=query, top_k=5)
    bm25_results = bm25r.query(query=query, top_k=5)
    rrf_results = rrf(vector_results, bm25_results, k=3, m=60)
    
    # 构建上下文
    context = "\n\n".join([text for text, score in rrf_results])
    
    # 生成答案
    print("\n生成答案...")
    prompt = f"""
    用户查询: {query}
    
    通过RRF算法检索到的相关文档:
    {context}
    
    请根据上述文档回答用户的查询。
    """
    
    response = rag_agent.step(prompt)
    final_answer = response.msgs[0].content.strip()
    
    print(f"\n最终答案:")
    print("-" * 40)
    print(final_answer)
    print("-" * 40)

if __name__ == "__main__":
    # 演示基础RRF功能
    demo_rrf_retrieval()
    
    # 演示RRF结合LLM生成
    demo_with_llm_generation()
    
    print("\n" + "=" * 60)
    print("RRF演示完成！")
    print("=" * 60)

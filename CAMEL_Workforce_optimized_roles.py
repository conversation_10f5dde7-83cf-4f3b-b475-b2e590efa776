from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType, TaskType
from camel.societies.workforce import Workforce
from camel.toolkits import SearchToolkit
from camel.messages import BaseMessage
from camel.tasks import Task
import os
import time
import asyncio
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 创建模型实例
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

print("🔧 正在创建角色优化版 Workforce...")

#--------------------------------------------------
# 自定义搜索工具类
#--------------------------------------------------

class RateLimitedSearchToolkit:
    def __init__(self, delay_seconds=3):
        self.search_toolkit = SearchToolkit()
        self.delay_seconds = delay_seconds
        self.last_search_time = 0
    
    def safe_search_duckduckgo(self, query: str, max_results: int = 5):
        current_time = time.time()
        time_since_last = current_time - self.last_search_time
        
        if time_since_last < self.delay_seconds:
            sleep_time = self.delay_seconds - time_since_last
            print(f"⏳ 等待 {sleep_time:.1f} 秒以避免速率限制...")
            time.sleep(sleep_time)
        
        try:
            result = self.search_toolkit.search_duckduckgo(query, max_results)
            self.last_search_time = time.time()
            print(f"✅ 搜索成功: {query[:50]}...")
            return result
        except Exception as e:
            print(f"⚠️ 搜索失败: {str(e)}")
            return f"搜索暂时不可用，请使用已有知识回答。错误: {str(e)}"

safe_search = RateLimitedSearchToolkit(delay_seconds=5)

#--------------------------------------------------
# 重新定义明确分工的工作节点
#--------------------------------------------------

# 1. 信息收集专员 - 只负责收集信息，不制定行程
information_collector_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="东京旅游信息收集专员",
        content="""你是专业的东京旅游信息收集专员。你的职责是收集和整理旅游相关信息，但不制定具体行程。

        🎯 核心职责：
        1. 收集东京热门景点的基本信息（地址、开放时间、门票价格）
        2. 搜集特色美食和餐厅推荐（位置、价格区间、特色菜品）
        3. 整理交通方式和路线信息（地铁线路、交通卡、出行建议）
        4. 收集购物场所信息（商场、特色街区、营业时间）
        5. 提供实用的旅游贴士（天气、文化礼仪、注意事项）

        📋 输出格式要求：
        - 按类别整理信息（景点、美食、交通、购物、贴士）
        - 提供具体的实用信息（时间、价格、地址）
        - 不要制定具体的行程安排
        - 不要给出游览顺序建议
        - 专注于信息的准确性和实用性

        ⚠️ 重要限制：
        - 只收集信息，不做行程规划
        - 不要安排具体的时间表
        - 不要建议游览路线"""
    ),
    model=model,
    tools=[safe_search.safe_search_duckduckgo]
)

# 2. 行程规划师 - 专门负责制定行程
itinerary_planner_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="专业行程规划师",
        content="""你是专业的东京旅游行程规划师。你的职责是根据信息收集专员提供的信息，制定详细的旅游行程。

        🎯 核心职责：
        1. 根据收集到的信息制定3日详细行程
        2. 合理安排每日的时间分配和景点顺序
        3. 考虑地理位置和交通便利性
        4. 平衡传统文化与现代体验
        5. 安排合适的用餐和休息时间

        📋 规划原则：
        - 每天安排3-4个主要活动，避免过于紧凑
        - 考虑景点间的距离和交通时间
        - 合理分配传统景点和现代景点
        - 包含购物和美食体验时间
        - 预留适当的自由活动时间

        📝 输出格式：
        ```
        ## 东京3日旅游行程规划

        ### 第一天：[主题]
        **上午 (9:00-12:00)**
        - 景点：[具体景点名称]
        - 活动：[具体活动内容]
        - 交通：[如何到达]
        - 预计时间：[停留时长]

        **中午 (12:00-13:30)**
        - 用餐：[推荐餐厅/美食]
        - 位置：[具体位置]

        **下午 (13:30-17:00)**
        - 景点：[具体景点名称]
        - 活动：[具体活动内容]
        - 交通：[交通方式]

        **晚上 (17:00-21:00)**
        - 活动：[晚间安排]
        - 用餐：[晚餐推荐]
        ```

        ⚠️ 重要要求：
        - 必须使用标准格式输出
        - 时间安排要具体明确
        - 包含详细的交通指引"""
    ),
    model=model
)

# 3. 质量评审员 - 专门负责评估
quality_reviewer_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="旅游行程质量评审员",
        content="""你是资深的旅游行程质量评审员。你的职责是评估行程的合理性并提出具体的改进建议。

        🎯 评审维度：
        1. **时间合理性**：每日安排是否过于紧凑或松散
        2. **交通便利性**：景点间的交通是否便利，路线是否合理
        3. **文化平衡性**：传统与现代体验的平衡
        4. **实用性**：行程是否适合首次访问东京的游客
        5. **预算合理性**：费用预算是否符合中等水平

        📋 输出格式：
        ```
        ## 行程质量评审报告

        ### 整体评分：[X/10分]

        ### 详细评估：
        **时间安排** [X/10分]
        - 优点：[具体优点]
        - 问题：[具体问题]

        **交通便利性** [X/10分]
        - 优点：[具体优点]
        - 问题：[具体问题]

        **文化体验** [X/10分]
        - 优点：[具体优点]
        - 问题：[具体问题]

        ### 具体改进建议：
        1. [具体建议1]
        2. [具体建议2]
        3. [具体建议3]

        ### 优化重点：
        - [重点1]
        - [重点2]
        ```

        ⚠️ 评审要求：
        - 必须给出具体的评分
        - 指出明确的问题和改进方向
        - 建议要具体可操作"""
    ),
    model=model
)

# 4. 最终整合专员 - 新增角色，负责生成标准化最终版本
final_integrator_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="最终行程整合专员",
        content="""你是最终行程整合专员。你的职责是整合所有信息和建议，生成标准化的最终旅游行程。

        🎯 核心职责：
        1. 整合信息收���专员的基础信息
        2. 采纳行程规划师的时间安排
        3. 应用质量评审员的改进建议
        4. 生成统一格式的最终行程

        📋 最终输出标准格式：
        ```
        # 东京3日旅游行程 - 最终版本

        ## 行程概览
        - 适合人群：[目标用户]
        - 预算水平：[预算范围]
        - 主要特色：[行程亮点]

        ## 详细行程

        ### 第一天：[主题名称]
        **时间安排**
        - 09:00-12:00 | [景点名称] 
          - 地址：[具体地址]
          - 交通：[详细交通方式]
          - 亮点：[主要看点]
          - 预算：[大概费用]

        - 12:00-13:30 | 午餐时间
          - 推荐：[具体餐厅名称]
          - 特色：[招牌菜品]
          - 预算：[用餐费用]

        [继续其他时间段...]

        ## 实用信息
        ### 交通指南
        - 推荐交通卡：[具体建议]
        - 主要线路：[地铁线路]

        ### 预算参考
        - 交通费：[具体金额]
        - 门票费：[具体金额]
        - 餐饮费：[具体金额]
        - 总预算：[总计金额]

        ### 贴心提醒
        - [实用提醒1]
        - [实用提醒2]
        ```

        ⚠️ 整合要求：
        - 必须采用统一的标准格式
        - 整合所有专员的建议和信息
        - 确保信息的完整性和准确性
        - 提供实用的预算和交通信息"""
    ),
    model=model
)

print("✅ 所有角色明确分工的工作节点已定义")

#--------------------------------------------------
# 创建优化版 Workforce
#--------------------------------------------------

coordinator_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="工作组协调员",
        content="""你是工作组协调员，负责协调四个专业角色的工作：
        1. 信息收集专员 - 收集基础旅游信息
        2. 行程规划师 - 制定具体行程安排
        3. 质量评审员 - 评估行程质量并提出改进建议
        4. 最终整合专员 - 生成标准化最终版本
        
        确保每个角色按照既定职责工作，避免职责重叠。"""
    ),
    model=model
)

from camel.toolkits import TaskPlanningToolkit
task_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="任务规划专家",
        content="你负责将旅游规划任务分解为四个明确的子任务，确保工作流程清晰有序。"
    ),
    model=model,
    tools=TaskPlanningToolkit().get_tools()
)

# 创建 Workforce
travel_workforce = Workforce(
    description="东京旅游规划工作组（角色优化版）",
    coordinator_agent=coordinator_agent,
    task_agent=task_agent
)

# 按照工作流程顺序添加节点
travel_workforce.add_single_agent_worker(
    "信息收集专员：收集东京旅游的基础信息，包括景点、美食、交通、购物等信息",
    worker=information_collector_agent
).add_single_agent_worker(
    "行程规划师：根据收集的信息制定详细的3日旅游行程安排",
    worker=itinerary_planner_agent
).add_single_agent_worker(
    "质量评审员：评估行程质量并提出具体的改进建议",
    worker=quality_reviewer_agent
).add_single_agent_worker(
    "最终整合专员：整合所有信息和建议，生成标准化的最终旅游行程",
    worker=final_integrator_agent
)

print("✅ 角色优化版 Workforce 实例已创建")

#--------------------------------------------------
# 创建和处理任务
#--------------------------------------------------

optimized_tokyo_task = Task(
    content="""请为首次访问东京的游客规划一份专业的3日旅游行程。

    工作流程要求：
    1. 信息收集专员：收集东京旅游基础信息
    2. 行程规划师：制定具体的3日行程安排
    3. 质量评审员：评估行程质量并提出改进建议
    4. 最终整合专员：生成标准化的最终版本

    确保每个角色按照既定职责工作，避免重复内容。""",
    
    additional_info={
        "目标用户": "25-30岁首次访问东京的游客",
        "兴趣偏好": "现代与传统结合，购物和美食",
        "预算水平": "中等预算，注重性价比",
        "当前日期": "2025年7月17日",
        "旅行天数": "3天",
        "特殊要求": "角色分工明确，输出格式统一"
    },
    id="optimized_tokyo_trip_roles"
)

print(f"\n🚀 开始处理角色优化任务...")

try:
    print("\n📋 工作流程：")
    print("1️⃣ 信息收集专员 - 收集基础信息")
    print("2️⃣ 行程规划师 - 制定详细行程")
    print("3️⃣ 质量评审员 - 评估并提出改进建议")
    print("4️⃣ 最终整合专员 - 生成标准化最终版本")
    
    processed_task = travel_workforce.process_task(optimized_tokyo_task)
    
    print("\n" + "="*100)
    print("🎯 角色优化任务处理完成")
    print("="*100)
    print(f"任务ID: {processed_task.id}")
    print(f"\n🌟 最终标准化行程:")
    print("-" * 90)
    print(processed_task.result)
    print("-" * 90)
    print("\n🎉 角色优化版 Workforce 演示成功！")
    print("\n💡 优化特色：")
    print("   ✅ 角色职责明确分工，避免重复")
    print("   📋 标准化输出格式")
    print("   🔄 完整的工作流程")
    print("   🎯 最终整合所有建议")
    
except Exception as e:
    print(f"\n❌ 任务处理出现错误: {str(e)}")
    print("建议检查网络连接和模型配置")
"""
RAG评估系统测试脚本

该脚本用于测试新添加的评估功能，包括：
1. 检索模块评估（精确率、召回率、F1值）
2. 生成模块评估（BLEU、ROUGE、语义相似度）
3. 多种相似度计算方法对比（TF-IDF vs Embedding）
4. 不同检索方法的性能对比
"""

import os
from dotenv import load_dotenv

# 导入增强版RAG系统
from test_RAG_Enhanced import EnhancedRAGSystem

def test_evaluation_system():
    """测试评估系统"""
    
    print("=" * 80)
    print("RAG评估系统测试")
    print("=" * 80)
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("❌ 错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return
    
    try:
        # 初始化系统
        print("🚀 初始化增强版RAG系统...")
        rag_system = EnhancedRAGSystem(api_key)
        
        # 设置评估知识库
        print("📚 设置评估知识库...")
        rag_system.setup_evaluation_knowledge_base(chunk_size=100)
        
        # 获取测试用例
        test_cases = rag_system.get_evaluation_test_cases()
        print(f"📝 加载了 {len(test_cases)} 个测试用例")
        
        # 测试1: 单个查询评估
        print("\n" + "=" * 60)
        print("测试1: 单个查询评估")
        print("=" * 60)
        
        query = test_cases[0]["query"]
        expected_answers = test_cases[0]["expected_answers"]
        
        print(f"查询: {query}")
        print(f"预期答案: {expected_answers}")
        
        # 执行检索
        retrieved_results = rag_system.vector_retriever.query(query=query, top_k=3)
        retrieved_texts = [result["text"] for result in retrieved_results]
        
        print(f"检索到 {len(retrieved_texts)} 个结果")
        
        # 评估检索质量
        retrieval_eval = rag_system.evaluator.evaluate_retrieval(
            query, expected_answers, retrieved_texts, method='embedding'
        )
        
        print(f"检索评估结果:")
        print(f"  精确率: {retrieval_eval['precision']:.4f}")
        print(f"  召回率: {retrieval_eval['recall']:.4f}")
        print(f"  F1分数: {retrieval_eval['f1']:.4f}")
        print(f"  平均相似度: {retrieval_eval['avg_similarity']:.4f}")
        
        # 测试2: 相似度计算方法对比
        print("\n" + "=" * 60)
        print("测试2: 相似度计算方法对比")
        print("=" * 60)
        
        expected = expected_answers[0]
        retrieved = retrieved_texts[0][:200] + "..."  # 截取前200字符
        
        tfidf_sim = rag_system.evaluator.compute_similarity(expected, retrieved, 'tfidf')
        embedding_sim = rag_system.evaluator.compute_similarity(expected, retrieved, 'embedding')
        
        print(f"预期答案: {expected}")
        print(f"检索结果: {retrieved}")
        print(f"TF-IDF相似度: {tfidf_sim:.4f}")
        print(f"Embedding相似度: {embedding_sim:.4f}")
        
        # 测试3: 生成模块评估
        print("\n" + "=" * 60)
        print("测试3: 生成模块评估")
        print("=" * 60)
        
        # 生成答案
        generated_answer = rag_system.generate_answer(query, retrieved_results)
        print(f"生成的答案: {generated_answer}")
        
        # 评估生成质量
        generation_eval = rag_system.evaluator.evaluate_generation(
            query, generated_answer, expected_answers
        )
        
        print(f"生成评估结果:")
        print(f"  语义相似度: {generation_eval.get('semantic_similarity', 0):.4f}")
        print(f"  BLEU分数: {generation_eval.get('bleu_score', 0):.4f}")
        print(f"  质量分数: {generation_eval.get('quality_score', 0):.4f}")
        
        if generation_eval.get('rouge_scores'):
            rouge_scores = generation_eval['rouge_scores']
            print(f"  ROUGE-1: {rouge_scores.get('rouge1', 0):.4f}")
            print(f"  ROUGE-L: {rouge_scores.get('rougeL', 0):.4f}")
        
        # 测试4: 多查询批量评估
        print("\n" + "=" * 60)
        print("测试4: 多查询批量评估")
        print("=" * 60)
        
        # 定义检索函数
        def retrieval_func(q):
            return rag_system.vector_retriever.query(query=q, top_k=3)
        
        # 执行批量评估
        batch_results = rag_system.evaluator.evaluate_multiple_queries(
            test_cases[:3],  # 只测试前3个用例
            retrieval_func,
            threshold=0.3,
            method='embedding'
        )
        
        print(f"批量评估完成，处理了 {batch_results['num_queries']} 个查询")
        
        if 'retrieval_metrics' in batch_results:
            metrics = batch_results['retrieval_metrics']
            print(f"整体检索指标:")
            print(f"  平均精确率: {metrics['average_precision']:.4f}")
            print(f"  平均召回率: {metrics['average_recall']:.4f}")
            print(f"  平均F1分数: {metrics['average_f1']:.4f}")
            print(f"  平均相似度: {metrics['average_similarity']:.4f}")
        
        print("\n✅ 评估系统测试完成！")
        print("所有功能正常工作。")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_different_chunk_sizes():
    """测试不同文档分块大小对评估结果的影响"""
    
    print("\n" + "=" * 80)
    print("测试不同文档分块大小的影响")
    print("=" * 80)
    
    load_dotenv()
    api_key = os.getenv('MODELSCOPE_SDK_TOKEN')
    
    if not api_key:
        print("❌ 错误：请设置MODELSCOPE_SDK_TOKEN环境变量")
        return
    
    chunk_sizes = [50, 100, 200, 500]
    
    for chunk_size in chunk_sizes:
        print(f"\n测试分块大小: {chunk_size}")
        print("-" * 40)
        
        try:
            # 初始化系统
            rag_system = EnhancedRAGSystem(api_key)
            rag_system.setup_evaluation_knowledge_base(chunk_size=chunk_size)
            
            # 测试单个查询
            test_case = rag_system.get_evaluation_test_cases()[0]
            query = test_case["query"]
            expected_answers = test_case["expected_answers"]
            
            # 执行检索和评估
            retrieved_results = rag_system.vector_retriever.query(query=query, top_k=3)
            retrieved_texts = [result["text"] for result in retrieved_results]
            
            evaluation = rag_system.evaluator.evaluate_retrieval(
                query, expected_answers, retrieved_texts, method='embedding'
            )
            
            print(f"  检索到 {len(retrieved_texts)} 个结果")
            print(f"  F1分数: {evaluation['f1']:.4f}")
            print(f"  平均相似度: {evaluation['avg_similarity']:.4f}")
            
        except Exception as e:
            print(f"  ❌ 测试分块大小 {chunk_size} 时出错: {e}")

if __name__ == "__main__":
    # 运行基本测试
    test_evaluation_system()
    
    # 可选：测试不同分块大小
    test_chunk_sizes = input("\n是否测试不同文档分块大小的影响？(y/n): ").strip().lower()
    if test_chunk_sizes == 'y':
        test_different_chunk_sizes()
    
    print("\n🎉 所有测试完成！")

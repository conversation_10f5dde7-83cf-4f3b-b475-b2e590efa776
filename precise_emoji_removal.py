#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
精确删除emoji，保持代码格式不变
"""

def remove_emojis_precisely(file_path):
    """精确删除emoji，保持格式"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 只删除特定的emoji，不使用正则表达式
    emoji_list = [
        '✅', '🎯', '📊', '🔍', '💾', '🚀', '🏃', '⚠️', '❌', 
        '💡', '📋', '🔧', '📈', '📞', '💰', '⏰', '🚇', '⭐', 
        '📝', '🏟️', '📍', '🎉', '🔄', '⏳', '🎭', '👤', '🏆'
    ]
    
    # 逐个替换emoji
    for emoji in emoji_list:
        content = content.replace(emoji, '')
    
    # 清理连续的空格（但保持缩进）
    lines = content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # 保持行首的空格（缩进），只清理行中的多余空格
        if line.strip():  # 非空行
            # 获取行首空格
            leading_spaces = len(line) - len(line.lstrip())
            # 清理行内容中的多余空格
            cleaned_content = ' '.join(line.strip().split())
            # 重新组合
            cleaned_line = ' ' * leading_spaces + cleaned_content
            cleaned_lines.append(cleaned_line)
        else:  # 空行
            cleaned_lines.append('')
    
    content = '\n'.join(cleaned_lines)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已精确删除 {file_path} 中的emoji")

if __name__ == "__main__":
    remove_emojis_precisely("CAMEL_Prompt_task2_workforce.py")
    print("精确emoji删除完成！")
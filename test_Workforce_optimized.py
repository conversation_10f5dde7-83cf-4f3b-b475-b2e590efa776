from camel.agents import ChatAgent
from camel.models import ModelFactory
from camel.types import ModelPlatformType, TaskType
from camel.societies.workforce import Workforce
from camel.messages import BaseMessage
from camel.tasks import Task
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 创建模型实例
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

print("🔧 正在创建优化版本的 Workforce...")

#--------------------------------------------------
# 定义工作节点 (不使用搜索工具，避免速率限制)
#--------------------------------------------------

# 1. 研究员 Agent：使用内置知识提供信息
researcher_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="东京旅游专家",
        content="""你是一位资深的东京旅游专家，拥有丰富的东京旅游知识。你熟悉东京的热门景点、特色美食、交通方式和购物场所。
        你的职责是基于你的专业知识，为游客提供准确、实用的东京旅游信息，包括：
        - 融合现代与传统的热门景点
        - 特色美食推荐和餐厅位置
        - 便捷的交通方式和路线
        - 适合购物的区域和商场
        请提供详细、实用的建议，不需要进行网络搜索。"""
    ),
    model=model
)

# 2. 规划师 Agent：负责行程规划
planner_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="高级旅游规划师",
        content="""你是一位经验丰富的高级旅游规划师，专门制定东京旅游行程。
        你的职责是根据提供的信息和用户需求，制定合理、详细且富有吸引力的旅游行程。
        你需要考虑：
        - 时间安排的合理性
        - 交通便利性和路线优化
        - 景点的特色和游客体验
        - 餐饮和购物的平衡安排
        - 现代与传统文化的结合体验"""
    ),
    model=model
)

# 3. 评审员 Agent：负责评估和优化
reviewer_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="资深旅行评论员",
        content="""你是一位眼光独到的资深旅行评论员，专门评估旅游行程质量。
        你的职责是从真正旅行者的角度，严格评审旅游行程，并提出建设性改进意见。
        评估维度包括：
        - 实用性：交通是否便利，时间安排是否合理
        - 趣味性：景点是否有吸引力，体验是否丰富
        - 性价比：费用预算是否合理
        - 潜在问题：可能遇到的困难和解决方案
        - 文化体验：是否能深入了解日本文化"""
    ),
    model=model
)

print("✅ 所有工作节点已定义（无搜索工具依赖）")

#--------------------------------------------------
# 创建简化的 Workforce 实例
#--------------------------------------------------

# 创建协调器 Agent
coordinator_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="工作组协调员",
        content="你是一个高效的工作组协调员，负责协调旅游规划团队的工作。你需要合理分配任务，确保工作流程顺畅，最终产出高质量的旅游行程规划。"
    ),
    model=model
)

# 创建任务代理 Agent
from camel.toolkits import TaskPlanningToolkit
task_agent = ChatAgent(
    system_message=BaseMessage.make_assistant_message(
        role_name="任务规划专家",
        content="你是一个专业的任务规划专家，负责将复杂的旅游规划任务分解为具体的子任务。你擅长分析任务需求，识别关键步骤，并合理安排任务执行顺序。"
    ),
    model=model,
    tools=TaskPlanningToolkit().get_tools()
)

# 创建 Workforce 实例（完整版本）
travel_workforce = Workforce(
    description="东京旅游行程规划与评估工作组",
    coordinator_agent=coordinator_agent,
    task_agent=task_agent
)

# 添加工作节点
travel_workforce.add_single_agent_worker(
    "提供东京的景点、美食和交通专业知识",
    worker=researcher_agent
).add_single_agent_worker(
    "制定详细的东京3日旅游行程",
    worker=planner_agent
).add_single_agent_worker(
    "评估旅游行程并提出改进建议",
    worker=reviewer_agent
)

print("✅ Workforce 实例已创建并添加所有工作节点")

#--------------------------------------------------
# 创建任务并启动
#--------------------------------------------------

tokyo_task = Task(
    content="请为一位首次访问东京的游客规划一份详细的3日旅游行程。行程应包含热门景点、特色美食体验，并考虑交通便利性。",
    additional_info={
        "游客偏好": "希望体验东京的现代与传统结合，对购物和美食有兴趣",
        "当前日期": "2025年7月17日",
        "旅行天数": "3天",
        "游客类型": "首次访问东京的游客",
        "特殊要求": "避免过于紧凑的行程，注重文化体验"
    },
    id="tokyo_trip_plan_optimized"
)

print(f"\n🚀 开始处理优化任务: '{tokyo_task.content[:50]}...'")

try:
    # 处理任务
    processed_task = travel_workforce.process_task(tokyo_task)
    
    print("\n" + "="*60)
    print("📋 任务处理完成")
    print("="*60)
    print(f"任务ID: {processed_task.id}")
    print(f"\n最终结果:\n{processed_task.result}")
    print("\n🎉 优化版 Workforce 演示成功！")
    
except Exception as e:
    print(f"\n❌ 任务处理出现错误: {str(e)}")
    print("这可能是由于模型配置或网络问题导致的")
from camel.agents import TaskSpecifyAgent
from camel.models import ModelFactory
from camel.prompts import TextPrompt
from camel.types import ModelPlatformType, TaskType
import os
import random
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 创建模型实例
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)


# AI患者Agent - 随机生成患者案例


class AIPatientAgent:
    def __init__(self, model):
        self.model = model
        
        # 定义AI患者的提示模板
        self.patient_prompt = TextPrompt(
            """你是一位需要健康咨询的AI患者。请根据以下信息生成一个真实的患者案例：
            
            年龄范围：{age_range}
            身份职业：{occupation}
            主要症状：{symptoms}
            潜在原因：{potential_causes}
            
            请以第一人称的方式描述你的情况，包括：
            1. 基本信息（年龄、职业、生活方式）
            2. 具体症状和持续时间
            3. 对健康的担忧和期望
            4. 目前的生活习惯（饮食、运动、睡眠等）
            
            请保持真实、详细，就像真正的患者在描述自己的健康状况一样。
            """
        )
        
        # 创建AI患者的任务Agent
        self.patient_agent = TaskSpecifyAgent(
            model=self.model,
            task_type=TaskType.AI_SOCIETY,
            task_specify_prompt=self.patient_prompt,
            output_language='ch'
        )
        
        # 随机案例生成的模板库
        self.case_templates = {
            "age_ranges": [
                "18-25岁", "26-35岁", "36-45岁", "46-55岁", "56-65岁", "65岁以上"
            ],
            "occupations": [
                "大学生", "程序员", "销售经理", "教师", "医生", "律师",
                "会计师", "设计师", "工程师", "护士", "司机", "厨师",
                "记者", "公务员", "退休人员", "家庭主妇", "创业者"
            ],
            "symptoms": [
                "长期疲劳和精神不振", "颈椎疼痛和肩膀僵硬", "失眠和睡眠质量差",
                "消化不良和胃部不适", "体重增加和代谢缓慢", "头痛和注意力不集中",
                "腰背疼痛", "血压偏高", "血糖不稳定", "情绪波动大",
                "皮肤问题", "记忆力下降", "关节疼痛", "呼吸困难",
                "心悸和胸闷", "月经不调", "更年期症状", "视力疲劳"
            ],
            "potential_causes": [
                "长期久坐办公", "工作压力过大", "饮食不规律", "缺乏运动",
                "熬夜和作息紊乱", "过度使用电子产品", "营养不均衡",
                "情绪压力", "环境污染", "遗传因素", "年龄增长",
                "激素变化", "不良生活习惯", "社交压力", "经济压力"
            ]
        }
    
    def generate_random_case(self):
        """随机生成一个患者案例"""
        case_info = {
            "age_range": random.choice(self.case_templates["age_ranges"]),
            "occupation": random.choice(self.case_templates["occupations"]),
            "symptoms": random.choice(self.case_templates["symptoms"]),
            "potential_causes": random.choice(self.case_templates["potential_causes"])
        }
        
        print(f"\n 随机生成患者案例：")
        print(f"年龄：{case_info['age_range']}")
        print(f"职业：{case_info['occupation']}")
        print(f"症状：{case_info['symptoms']}")
        print(f"潜在原因：{case_info['potential_causes']}")
        print("-" * 50)
        
        return case_info
    
    def create_patient_profile(self, case_info=None):
        """创建患者档案"""
        if case_info is None:
            case_info = self.generate_random_case()
        
        task_prompt = "生成一个真实的患者案例描述"
        
        response = self.patient_agent.run(
            task_prompt=task_prompt,
            meta_dict=case_info
        )
        
        print("\n AI患者的自述：")
        print(response)
        print("-" * 50)
        
        return response, case_info


# AI专业健康顾问Agent - 思维链分析


class AIHealthAdvisorAgent:
    def __init__(self, model):
        self.model = model
        
        # 定义AI健康顾问的提示模板
        self.advisor_prompt = TextPrompt(
            """你是一位经验丰富的AI专业健康顾问。面对患者的描述，请使用思维链方式进行专业分析。
            
            患者描述：{patient_description}
            
            请按照以下思维链步骤进行分析：
            
            思维链步骤1：病情分析
            - 仔细分析患者的症状和描述
            - 识别主要健康问题和次要问题
            - 评估症状的严重程度和紧急性
            - 推测可能的病因和风险因素
            
            思维链步骤2：诊断推理
            - 基于症状进行初步诊断推理
            - 考虑可能的疾病或健康状况
            - 分析生活方式对健康的影响
            - 确定需要重点关注的健康指标
            
            思维链步骤3：营养干预策略
            - 分析当前饮食习惯的问题
            - 制定个性化的营养改善方案
            - 推荐具体的食物和营养补充
            - 提供饮食时间和分量建议
            
            思维链步骤4：运动康复方案
            - 评估患者的运动能力和限制
            - 设计适合的运动类型和强度
            - 制定循序渐进的锻炼计划
            - 强调运动安全和注意事项
            
            思维链步骤5：生活方式调整
            - 分析作息和生活习惯问题
            - 提供睡眠质量改善建议
            - 制定压力管理策略
            - 建议环境和工作习惯的调整
            
            思维链步骤6：监测和随访计划
            - 设定健康监测指标
            - 制定定期检查计划
            - 建立健康改善的里程碑
            - 提供长期健康维护建议
            
            请以专业、细致、关怀的态度提供建议，确保每个步骤都有清晰的逻辑和实用的建议。
            """
        )
        
        # 创建AI健康顾问的任务Agent
        self.advisor_agent = TaskSpecifyAgent(
            model=self.model,
            task_type=TaskType.AI_SOCIETY,
            task_specify_prompt=self.advisor_prompt,
            output_language='ch'
        )
    
    def analyze_patient_case(self, patient_description):
        """使用思维链方式分析患者案例"""
        print("\n AI专业健康顾问开始思维链分析：")
        print("=" * 60)
        
        task_prompt = "请对患者案例进行专业的思维链分析"
        
        response = self.advisor_agent.run(
            task_prompt=task_prompt,
            meta_dict={"patient_description": patient_description}
        )
        
        print(response)
        print("=" * 60)
        
        return response
    
    def provide_quick_assessment(self, patient_description):
        """提供快速评估"""
        quick_prompt = TextPrompt(
            """作为专业健康顾问，请对患者情况进行快速评估：
            
            患者描述：{patient_description}
            
            请简要提供：
            1. 主要健康问题识别
            2. 风险等级评估（低/中/高）
            3. 紧急程度判断
            4. 建议的下一步行动
            """
        )
        
        quick_agent = TaskSpecifyAgent(
            model=self.model,
            task_type=TaskType.AI_SOCIETY,
            task_specify_prompt=quick_prompt,
            output_language='ch'
        )
        
        response = quick_agent.run(
            task_prompt="快速评估患者健康状况",
            meta_dict={"patient_description": patient_description}
        )
        
        print("\n 快速评估结果：")
        print(response)
        print("-" * 50)
        
        return response


# 健康咨询会话系统


class HealthConsultationSystem:
    def __init__(self, model):
        self.ai_patient = AIPatientAgent(model)
        self.ai_advisor = AIHealthAdvisorAgent(model)
        self.consultation_history = []
    
    def start_consultation(self, use_random_case=True, custom_case=None):
        """开始健康咨询会话"""
        print("\n AI健康咨询系统启动")
        print("=" * 60)
        
        # 生成或使用患者案例
        if use_random_case:
            patient_description, case_info = self.ai_patient.create_patient_profile()
        else:
            patient_description, case_info = self.ai_patient.create_patient_profile(custom_case)
        
        # 健康顾问快速评估
        quick_assessment = self.ai_advisor.provide_quick_assessment(patient_description)
        
        # 健康顾问详细分析
        detailed_analysis = self.ai_advisor.analyze_patient_case(patient_description)
        
        # 保存咨询记录
        consultation_record = {
            "case_info": case_info,
            "patient_description": patient_description,
            "quick_assessment": quick_assessment,
            "detailed_analysis": detailed_analysis
        }
        
        self.consultation_history.append(consultation_record)
        
        print("\n 健康咨询完成！")
        print("=" * 60)
        
        return consultation_record
    
    def batch_consultation(self, num_cases=3):
        """批量进行健康咨询"""
        print(f"\n 开始批量健康咨询 ({num_cases} 个案例)")
        print("=" * 60)
        
        for i in range(num_cases):
            print(f"\n【案例 {i+1}/{num_cases}】")
            self.start_consultation(use_random_case=True)
            
            # 案例间分隔
            if i < num_cases - 1:
                print("\n" + "" * 30)
    
    def get_consultation_summary(self):
        """获取咨询历史摘要"""
        if not self.consultation_history:
            print("暂无咨询记录")
            return
        
        print(f"\n 咨询历史摘要 (共{len(self.consultation_history)}个案例)")
        print("=" * 60)
        
        for i, record in enumerate(self.consultation_history):
            case_info = record["case_info"]
            print(f"\n案例 {i+1}:")
            print(f"  年龄：{case_info['age_range']}")
            print(f"  职业：{case_info['occupation']}")
            print(f"  症状：{case_info['symptoms']}")
            print(f"  原因：{case_info['potential_causes']}")
            print("-" * 30)

# ===============================
# 主程序运行
# ===============================

def main():
    # 创建健康咨询系统
    consultation_system = HealthConsultationSystem(model)
    
    print(" AI健康咨询系统")
    print("包含AI患者（随机生成案例）和AI专业健康顾问（思维链分析）")
    
    # 演示1：单次随机咨询
    print("\n" + "="*80)
    print(" 演示1：单次随机患者咨询")
    print("="*80)
    consultation_system.start_consultation(use_random_case=True)
    
    # 演示2：自定义案例咨询
    print("\n" + "="*80)
    print(" 演示2：自定义患者案例咨询")
    print("="*80)
    custom_case = {
        "age_range": "35-40岁",
        "occupation": "软件工程师",
        "symptoms": "长期颈椎疼痛和眼部疲劳",
        "potential_causes": "长期对着电脑工作"
    }
    consultation_system.start_consultation(use_random_case=False, custom_case=custom_case)
    
    # 演示3：批量咨询
    print("\n" + "="*80)
    print(" 演示3：批量随机咨询")
    print("="*80)
    consultation_system.batch_consultation(num_cases=2)
    
    # 显示咨询历史
    consultation_system.get_consultation_summary()

if __name__ == "__main__":
    main()
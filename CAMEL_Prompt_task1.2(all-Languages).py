from camel.agents import TaskSpecifyAgent
from camel.models import ModelFactory
from camel.prompts import TextPrompt, CodePromptTemplateDict
from camel.types import ModelPlatformType, TaskType
import os
import random # 导入 random 模块
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv('MODELSCOPE_SDK_TOKEN')

# 创建模型实例
model = ModelFactory.create(
    model_platform=ModelPlatformType.OPENAI_COMPATIBLE_MODEL,
    model_type="Qwen/Qwen2.5-72B-Instruct",
    url='https://api-inference.modelscope.cn/v1/',
    api_key=api_key
)

class AIProgrammingTutorAgent:
    def __init__(self, model):
        self.model = model
        
        # 定义可供选择的编程语言列表
        self.available_languages = ["Python", "Java", "JavaScript", "C++", "Go", "Rust"]

        # 定义 AI 编程导师的提示模板
        self.tutor_prompt_templates = CodePromptTemplateDict(
            {
                "learning_plan": TextPrompt(
                    """你是一位专业的编程导师，旨在帮助用户学习新的编程语言。
                    请为学习 **{language}** 制定一个详细的学习计划。
                    学习计划应该包括以下阶段，每个阶段都提供详细的说明、核心概念和建议的学习资源：

                    1. **基础语法和数据类型**:
                       - 核心概念:
                       - 建议学习资源:

                    2. **控制流和函数**:
                       - 核心概念:
                       - 建议学习资源:

                    3. **数据结构和算法初步**:
                       - 核心概念:
                       - 建议学习资源:

                    4. **面向对象编程 (OOP) 基础**:
                       - 核心概念:
                       - 建议学习资源:

                    5. **模块、包和错误处理**:
                       - 核心概念:
                       - 建议学习资源:

                    6. **文件操作和常用库**:
                       - 核心概念:
                       - 建议学习资源:

                    请确保计划循序渐进，易于理解和实践。
                    """
                ),
                "code_example": TextPrompt(
                    """请为 **{language}** 中的 **{concept}** 提供一个清晰、简洁的代码示例。
                    代码示例应包含必要的注释，并演示该概念的实际应用。
                    """
                ),
                "exercise_problem": TextPrompt(
                    """请为学习 **{language}** 中 **{concept}** 的用户设计一个练习题目。
                    题目应包含明确的描述、输入输出要求和预期结果的示例。
                    并提供一个参考答案（可以是一个简单的函数实现）。
                    """
                )
            }
        )
        
        # 创建 AI 编程导师的任务 Agent
        self.tutor_agent = TaskSpecifyAgent(
            model=self.model,
            task_type=TaskType.AI_SOCIETY, 
            task_specify_prompt=self.tutor_prompt_templates["learning_plan"], 
            output_language='ch'
        )

    def _get_random_language(self):
        """内部方法：随机选择一种编程语言"""
        return random.choice(self.available_languages)

    def generate_learning_plan(self, language=None):
        """
        为指定（或随机选择）编程语言生成详细的学习计划。
        如果未指定 language，将随机选择一种。
        """
        if language is None:
            language = self._get_random_language()

        print(f"\n为学习 **{language}** 生成学习计划：")
        print("=" * 60)
        
        self.tutor_agent.task_specify_prompt = self.tutor_prompt_templates["learning_plan"]
        
        response = self.tutor_agent.run(
            task_prompt=f"生成一个关于学习 {language} 的详细计划",
            meta_dict={"language": language}
        )
        
        print(response)
        print("=" * 60)
        return response, language # 返回语言，以便后续使用

    def generate_code_example(self, language, concept):
        """
        为指定编程语言中的某个概念生成代码示例。
        """
        print(f"\n为 **{language}** 中的 **{concept}** 生成代码示例：")
        print("-" * 50)
        
        self.tutor_agent.task_specify_prompt = self.tutor_prompt_templates["code_example"]
        
        response = self.tutor_agent.run(
            task_prompt=f"生成一个关于 {language} 中 {concept} 的代码示例",
            meta_dict={"language": language, "concept": concept}
        )
        
        print(response)
        print("-" * 50)
        return response

    def generate_exercise_problem(self, language, concept):
        """
        为指定编程语言中的某个概念设计练习题目，并提供参考答案。
        """
        print(f"\n为 **{language}** 中的 **{concept}** 设计练习题目：")
        print("-" * 50)
        
        self.tutor_agent.task_specify_prompt = self.tutor_prompt_templates["exercise_problem"]
        
        response = self.tutor_agent.run(
            task_prompt=f"为 {language} 中 {concept} 设计一个练习题目",
            meta_dict={"language": language, "concept": concept}
        )
        
        print(response)
        print("-" * 50)
        return response

## 主程序运行


def main():
    # 创建 AI 编程导师系统
    tutor_system = AIProgrammingTutorAgent(model)
    
    print("AI 编程学习助手 - 随机语言模式")
    print("帮助你逐步学习一门新的编程语言，语言会随机选择。")
    
    # 1. 生成一个随机语言的学习计划
    print("\n" + "="*80)
    print("生成随机编程语言的学习计划")
    print("="*80)
    # 调用时不再传递 language 参数，让其随机选择
    learning_plan_response, selected_language = tutor_system.generate_learning_plan() 
    print(f"本次选定的学习语言是：**{selected_language}**")

    # 为了让代码示例和练习题基于同一门随机选择的语言，我们使用上面返回的 selected_language
    
    # 2. 为该随机语言中的特定概念生成代码示例
    # 注意：这里需要你手动指定概念，因为概念是与语言相关的
    # 比如 Python 的“变量和数据类型”，Java 的“类和对象”
    # 在实际应用中，你可能需要一个更智能的方式来选择概念，或者让用户输入
    concept_for_example = ""
    if selected_language == "Python":
        concept_for_example = "函数和参数"
    elif selected_language == "Java":
        concept_for_example = "类与对象"
    elif selected_language == "JavaScript":
        concept_for_example = "异步编程"
    elif selected_language == "C++":
        concept_for_example = "指针"
    elif selected_language == "Go":
        concept_for_example = "Goroutine"
    elif selected_language == "Rust":
        concept_for_example = "所有权与借用"
    else:
        concept_for_example = "基础语法" # 兜底概念

    print("\n" + "="*80)
    print(f"生成 **{selected_language}** 中 '**{concept_for_example}**' 的代码示例")
    print("="*80)
    code_example_response = tutor_system.generate_code_example(selected_language, concept_for_example)

    # 3. 为该随机语言中的特定概念生成练习题目
    concept_for_exercise = ""
    if selected_language == "Python":
        concept_for_exercise = "条件判断"
    elif selected_language == "Java":
        concept_for_exercise = "接口"
    elif selected_language == "JavaScript":
        concept_for_exercise = "DOM操作"
    elif selected_language == "C++":
        concept_for_exercise = "模板"
    elif selected_language == "Go":
        concept_for_exercise = "错误处理"
    elif selected_language == "Rust":
        concept_for_exercise = "生命周期"
    else:
        concept_for_exercise = "循环结构" # 兜底概念

    print("\n" + "="*80)
    print(f"生成 **{selected_language}** 中 '**{concept_for_exercise}**' 的练习题目")
    print("="*80)
    exercise_problem_response = tutor_system.generate_exercise_problem(selected_language, concept_for_exercise)

if __name__ == "__main__":
    main()
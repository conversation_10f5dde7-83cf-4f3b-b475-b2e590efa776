{"recommendations": ["cweijan.dbclient-jdbc", "cweijan.vscode-mysql-client2", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "github.copilot", "github.copilot-chat", "hediet.vscode-drawio", "jebbs.plantuml", "marscode.marscode-extension", "mikestead.dotenv", "ms-ceintl.vscode-language-pack-zh-hans", "ms-dotnettools.csdevkit", "ms-dotnettools.csharp", "ms-dotnettools.vscode-dotnet-runtime", "ms-python.debugpy", "ms-python.python", "ms-python.vscode-pylance", "ms-vscode.cmake-tools", "ms-vscode.cpptools", "ms-vscode.cpptools-extension-pack", "ms-vscode.cpptools-themes", "redhat.java", "saoudrizwan.claude-dev", "sqlpassion.vscode-binutils", "twxs.cmake", "visualstudioexptteam.intellicode-api-usage-examples", "visualstudioexptteam.vscodeintellicode", "vscjava.vscode-gradle", "vscjava.vscode-java-debug", "vscjava.vscode-java-dependency", "vscjava.vscode-java-pack", "vscjava.vscode-java-test", "vscjava.vscode-maven", "yzhang.markdown-all-in-one"]}